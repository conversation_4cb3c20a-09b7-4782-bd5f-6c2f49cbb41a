server:
  port: 8080

# JWT签名配置
jwt:
  signer:
    # 签名方式选择: simple(简单对称加密) 或 aliyun-kms(阿里云KMS)
    type: simple
    # 简单对称加密配置
    simple:
      secret-key: ${JWT_SECRET_KEY:caidao-oauth2-jwt-signing-key-2023-very-long-secret-key-for-hmac-sha256}

spring:
  application:
    name: caidao-open-auth-service
#
#  # Database Configuration
#  datasource:
#    type: com.alibaba.druid.pool.DruidDataSource
#    driver-class-name: com.mysql.cj.jdbc.Driver
#    url: *****************************************************************************************************************************************************
#    username: ${DB_USERNAME:root}
#    password: ${DB_PASSWORD:password}
#    druid:
#      initial-size: 5
#      min-idle: 5
#      max-active: 20
#      max-wait: 60000
#      time-between-eviction-runs-millis: 60000
#      min-evictable-idle-time-millis: 300000
#      validation-query: SELECT 1 FROM DUAL
#      test-while-idle: true
#      test-on-borrow: false
#      test-on-return: false
#      pool-prepared-statements: true
#      max-pool-prepared-statement-per-connection-size: 20
#      filters: stat,wall,slf4j
#      connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
#      web-stat-filter:
#        enabled: true
#        url-pattern: /*
#        exclusions: "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*"
#      stat-view-servlet:
#        enabled: true
#        url-pattern: /druid/*
#        reset-enable: false
#        login-username: admin
#        login-password: admin123
#        allow: 127.0.0.1
#
#  # JPA Configuration
#  jpa:
#    hibernate:
#      ddl-auto: update
#    show-sql: false
#    properties:
#      hibernate:
#        dialect: org.hibernate.dialect.MySQL8Dialect
#        format_sql: true
#
#  # Redis Configuration (for token storage)
#  redis:
#    host: ${REDIS_HOST:localhost}
#    port: ${REDIS_PORT:6379}
#    password: ${REDIS_PASSWORD:}
#    database: 0
#    timeout: 3000ms
#    lettuce:
#      pool:
#        max-active: 8
#        max-idle: 8
#        min-idle: 0
#        max-wait: -1ms
#
## MyBatis Plus Configuration
#mybatis-plus:
#  mapper-locations: classpath*:/mapper/**/*.xml
#  type-aliases-package: com.caidao.auth.entity
#  configuration:
#    map-underscore-to-camel-case: true
#    cache-enabled: false
#    call-setters-on-nulls: true
#    jdbc-type-for-null: 'null'
#  global-config:
#    db-config:
#      id-type: auto
#      logic-delete-field: deleted
#      logic-delete-value: 1
#      logic-not-delete-value: 0
#
# Alibaba Cloud Configuration (当jwt.signer.type=aliyun-kms时需要配置)
aliyun:
  access-key-id: ${ALIYUN_ACCESS_KEY_ID:your-access-key-id}
  access-key-secret: ${ALIYUN_ACCESS_KEY_SECRET:your-access-key-secret}
  region-id: ${ALIYUN_REGION_ID:cn-hangzhou}
  endpoint: ${ALIYUN_KMS_ENDPOINT:kms.cn-hangzhou.aliyuncs.com}
  group-id: ${ALIYUN_API_GATEWAY_GROUP_ID:your-group-id}

  # KMS Configuration
  kms:
    key-id: ${ALIYUN_KMS_KEY_ID:your-kms-key-id}
    key-version-id: ${ALIYUN_KMS_KEY_VERSION_ID:your-key-version-id}
    alg: ${ALIYUN_KMS_ALGORITHM:RSA_PKCS1_SHA_256}
#
## OAuth2 Configuration (Client Credentials Only)
#oauth2:
#  jwt:
#    signing-key: ${JWT_SIGNING_KEY:caidao-oauth2-jwt-signing-key-2023}
#    access-token-validity-seconds: 3600  # 1 hour for client credentials
#
## Security Configuration
#security:
#  # Password encryption
#  password:
#    encoder: bcrypt
#    strength: 12
#
## Logging Configuration
#logging:
#  level:
#    com.caidao.auth: DEBUG
#    org.springframework.security.oauth2: DEBUG
#    org.springframework.web: INFO
#    org.hibernate.SQL: DEBUG
#  pattern:
#    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
#    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
#  file:
#    name: logs/caidao-auth.log
#    max-size: 100MB
#    max-history: 30
#
## Management Configuration
#management:
#  endpoints:
#    web:
#      exposure:
#        include: health,info,metrics,prometheus
#  endpoint:
#    health:
#      show-details: when-authorized
#  metrics:
#    export:
#      prometheus:
#        enabled: true
#
## Application Information
#info:
#  app:
#    name: Caidao Open Auth Service
#    description: OAuth 2.0 Client Credentials Service with Alibaba Cloud Integration
#    version: 1.0.0
#    grant-types: client_credentials
#    encoding: UTF-8
#    java:
#      version: 1.8
