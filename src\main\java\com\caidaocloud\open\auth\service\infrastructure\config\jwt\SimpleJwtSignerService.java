package com.caidaocloud.open.auth.service.infrastructure.config.jwt;

import com.caidaocloud.util.FastjsonUtil;
import io.jsonwebtoken.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.util.Base64;
import java.util.Map;

/**
 * 简单对称加密JWT签名服务实现
 * 使用HMAC-SHA256算法进行签名
 *
 * <AUTHOR>
 */
@Component
@Slf4j
@ConditionalOnProperty(name = "jwt.signer.type", havingValue = "simple", matchIfMissing = true)
public class SimpleJwtSignerService implements JwtSignerService {

    @Value("${jwt.signer.simple.secret-key}")
    private String secretKey;

    @Override
    public String sign(Map<String, Object> claims) {
        try {
            String jwt = Jwts.builder()
                    .setClaims(claims)
                    .signWith(SignatureAlgorithm.HS256, secretKey)
                    .compact();
            
            log.info("JWT signed using simple symmetric encryption");
            return jwt;
        } catch (Exception e) {
            log.error("Failed to sign JWT with simple encryption", e);
            throw new RuntimeException("JWT signing failed", e);
        }
    }

    @Override
    public boolean verify(JwsHeader header, String payload, String signature) {
        try {
            // 重新构建完整的JWT字符串
            String headerJson = FastjsonUtil.toJson(header);
            String headerBase64 = Base64.getUrlEncoder().withoutPadding().encodeToString(headerJson.getBytes());
            String jwtToken = headerBase64 + "." + payload + "." + signature;
            
            // 使用JJWT库验证签名
            Jwts.parser()
                    .setSigningKey(secretKey)
                    .parseClaimsJws(jwtToken);
            
            log.debug("JWT signature verified successfully using simple encryption");
            return true;
        } catch (JwtException e) {
            log.warn("JWT signature verification failed: {}", e.getMessage());
            return false;
        } catch (Exception e) {
            log.error("Unexpected error during JWT verification", e);
            return false;
        }
    }

    @Override
    public String getSignerType() {
        return "simple";
    }
}
