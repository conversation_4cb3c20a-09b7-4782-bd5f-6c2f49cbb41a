package com.caidaocloud.open.auth.service.infrastructure.config.aliyun;

import java.util.Base64;
import java.util.Map;

import javax.annotation.Resource;

import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.PreCheck;
import com.caidaocloud.security.dto.TokenDto;
import com.caidaocloud.security.token.TokenGenerator;
import com.caidaocloud.util.FastjsonUtil;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jws;
import io.jsonwebtoken.JwsHeader;
import io.jsonwebtoken.JwtHandlerAdapter;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import lombok.extern.slf4j.Slf4j;

import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.security.oauth2.provider.token.store.JwtAccessTokenConverter;
import org.springframework.stereotype.Component;
import org.springframework.util.MultiValueMap;

@Component
@Slf4j
public class AliyunKmsJwtClientAuthConverter extends JwtAccessTokenConverter {

	@Resource
	private KmsJwtSigner kmsJwtSigner;

	@Override
	protected String encode(OAuth2AccessToken accessToken, OAuth2Authentication authentication) {
		Map<String, Object> userInfoMap = null;
		// accessToken 身份信息为空，转换 accessToken 失败
		PreCheck.preCheckArgument(null == accessToken || null == (userInfoMap = accessToken.getAdditionalInformation()),
				"accessToken is empty, convert access token err");

		Object userid = userInfoMap.get("userid"), belongId = userInfoMap.get("belongId");
		// accessToken 中缺少身份信息
		PreCheck.preCheckArgument(null == userid || null == belongId, "Identity information is missing in accesstoken");

		String tenantId = belongId.toString(), userId = userid.toString();

		String jwt = kmsJwtSigner.sign(new TokenDto(userId, tenantId, 2).getMap());
		log.info("jwt acquired,tenantId={}", tenantId);
		return jwt;
	}

	@Override
	protected Map<String, Object> decode(String jwt) {
		Jws<Claims> jws = Jwts.parser().parseClaimsJws(jwt);
		JwsHeader header = jws.getHeader();
		String payload = jwt.substring(0, jwt.lastIndexOf('.'));
		String signature = jws.getSignature();
		kmsJwtSigner.verify(header, payload, signature);
		return jws.getBody();
	}


}