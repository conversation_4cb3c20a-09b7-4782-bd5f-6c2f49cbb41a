package com.caidaocloud.open.auth.service.interfaces.facade;

import com.caidaocloud.open.auth.service.application.dto.ClientCredentialsRequest;
import com.caidaocloud.open.auth.service.application.dto.TokenResponse;
import com.caidaocloud.open.auth.service.application.service.OAuthApplicationService;
import com.caidaocloud.web.Result;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;

/**
 * OAuth2 REST Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/oauth")
@Deprecated
public class OAuth2Controller {

    private static final Logger logger = LoggerFactory.getLogger(OAuth2Controller.class);

    @Autowired
    private OAuthApplicationService oauthApplicationService;

    /**
     * OAuth2 Token Endpoint (Client Credentials Grant)
     */
    @PostMapping("/token")
    public Result<TokenResponse> getToken(@Valid @RequestBody ClientCredentialsRequest request) {
        try {
            TokenResponse tokenResponse = oauthApplicationService.handleClientCredentials(request);
            logger.info("Access token generated successfully for client: {}", request.getClientId());
            return Result.ok(tokenResponse);
        } catch (Exception e) {
            logger.error("Failed to generate access token for client: {}", request.getClientId(), e);
            return Result.fail("Token generation failed: " + e.getMessage());
        }
    }


}
