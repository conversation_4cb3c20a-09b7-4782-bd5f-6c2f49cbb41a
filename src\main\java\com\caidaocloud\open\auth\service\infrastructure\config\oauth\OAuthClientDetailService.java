package com.caidaocloud.open.auth.service.infrastructure.config.oauth;

import java.util.Arrays;
import java.util.Optional;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

import com.caidaocloud.cache.CacheService;
import com.caidaocloud.open.auth.service.domain.entity.OAuthClient;
import com.caidaocloud.open.auth.service.domain.repository.OAuthClientRepository;
import com.caidaocloud.util.FastjsonUtil;
import lombok.extern.slf4j.Slf4j;

import org.springframework.security.oauth2.provider.ClientDetails;
import org.springframework.security.oauth2.provider.ClientDetailsService;
import org.springframework.security.oauth2.provider.ClientRegistrationException;
import org.springframework.stereotype.Service;

/**
 * 加载要认证的客户端信息
 * <AUTHOR>
 * @date 2021-11-11
 */
@Service
@Slf4j
public class OAuthClientDetailService implements ClientDetailsService {

    @Resource
    private OAuthClientRepository oauthClientRepository;

    @Resource
    private CacheService cacheService;

    // @PostConstruct
    // public void init() {
    //     List<OAuthClientDetailsDo> clientList = oauthClientDetailRepository.selectListAll();
    //     clientList.stream().forEach(oauthClient -> {
    //         String oauthClientKey = String.format(OAUTH2_CLIENT_KEY, oauthClient.getClientId());
    //         cacheService.cacheValue(oauthClientKey, FastjsonUtil.toJson(oauthClient));
    //     });
    // }

    @Override
    public ClientDetails loadClientByClientId(String clientId) throws ClientRegistrationException {
        Optional<OAuthClient> optional = oauthClientRepository.findByClientId(clientId);
        if(!optional.isPresent()){
            throw new ClientRegistrationException("The app is disabled or does not exist");
        }
        OAuthClient oauth2Client = optional.get();

        ClientDetail clientDetail = new ClientDetail();
        clientDetail.setTenantId(oauth2Client.getTenantId());
        clientDetail.setClientId(oauth2Client.getClientId());
        clientDetail.setClientSecret(oauth2Client.getClientSecret());
        clientDetail.setAccessTokenValiditySeconds(oauth2Client.getAccessTokenValidity());
        clientDetail.setRefreshTokenValiditySeconds(oauth2Client.getRefreshTokenValidity());
        clientDetail.setAuthorizedGrantTypes(Arrays.asList(oauth2Client.getAuthorizedGrantTypes().split(",")));
        clientDetail.setScope(Arrays.asList(oauth2Client.getScope()));

        /***
         * 设置这个会默认的跳过授权页面
         */
        // clientDetail.setAutoApproveScopes(Arrays.asList("all"));

        return clientDetail;
    }

    public OAuthClient loadClientById(String clientId){
        return oauthClientRepository.findByClientId(clientId).get();
    }
}
