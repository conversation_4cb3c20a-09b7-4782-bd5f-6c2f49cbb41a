package com.caidaocloud.open.auth.service.domain.repository;

import com.caidaocloud.open.auth.service.domain.entity.OAuthClient;

import java.util.List;
import java.util.Optional;

/**
 * OAuth Client Repository Interface (Domain Layer)
 * 
 * <AUTHOR>
 */
public interface OAuthClientRepository {

    /**
     * Find client by client ID
     */
    Optional<OAuthClient> findByClientId(String clientId);

    /**
     * Save or update client
     */
    OAuthClient save(OAuthClient client);

    /**
     * Delete client by client ID
     */
    void deleteByClientId(String clientId);

    /**
     * Find all enabled clients
     */
    List<OAuthClient> findAllEnabled();

    /**
     * Check if client exists by client ID
     */
    boolean existsByClientId(String clientId);

    /**
     * Update client last used time
     */
    void updateLastUsed(String clientId);
}
