package com.caidaocloud.open.auth.service.infrastructure.config.jwt;

import org.junit.Test;

import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.*;

/**
 * JWT签名服务测试
 *
 * <AUTHOR>
 */
public class JwtSignerServiceTest {

    @Test
    public void testSimpleJwtSigner() {
        // 创建简单JWT签名服务实例
        SimpleJwtSignerService simpleJwtSigner = new SimpleJwtSignerService();
        // 通过反射设置secretKey字段
        try {
            java.lang.reflect.Field secretKeyField = SimpleJwtSignerService.class.getDeclaredField("secretKey");
            secretKeyField.setAccessible(true);
            secretKeyField.set(simpleJwtSigner, "test-secret-key-for-jwt-signing-very-long-key");
        } catch (Exception e) {
            fail("Failed to set secret key: " + e.getMessage());
        }

        // 准备测试数据
        Map<String, Object> claims = new HashMap<>();
        claims.put("userId", "12345");
        claims.put("tenantId", "tenant-001");
        claims.put("exp", System.currentTimeMillis() / 1000 + 3600); // 1小时后过期

        // 测试签名
        String jwt = simpleJwtSigner.sign(claims);
        assertNotNull(jwt);
        assertFalse(jwt.isEmpty());

        // JWT应该包含三个部分，用.分隔
        String[] parts = jwt.split("\\.");
        assertEquals("JWT should have 3 parts separated by dots", 3, parts.length);

        // 验证签名器类型
        assertEquals("simple", simpleJwtSigner.getSignerType());

        System.out.println("Generated JWT: " + jwt);
        System.out.println("Signer type: " + simpleJwtSigner.getSignerType());
    }
}
