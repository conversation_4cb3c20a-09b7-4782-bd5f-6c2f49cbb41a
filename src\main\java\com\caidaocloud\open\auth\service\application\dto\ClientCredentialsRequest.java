package com.caidaocloud.open.auth.service.application.dto;

import javax.validation.constraints.NotBlank;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Client Credentials Request DTO
 * 
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class ClientCredentialsRequest {

    @NotBlank(message = "Grant type is required")
    private String grantType;

    @NotBlank(message = "Client ID is required")
    private String clientId;

    @NotBlank(message = "Client secret is required")
    private String clientSecret;

    private String scope;

    public ClientCredentialsRequest(String grantType, String clientId, String clientSecret, String scope) {
        this.grantType = grantType;
        this.clientId = clientId;
        this.clientSecret = clientSecret;
        this.scope = scope;
    }
}
