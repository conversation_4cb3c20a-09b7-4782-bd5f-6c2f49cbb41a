package com.caidaocloud.open.auth.service.application.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Token Response DTO
 * 
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class TokenResponse {

    @JsonProperty("access_token")
    private String accessToken;

    @JsonProperty("token_type")
    private String tokenType;

    @JsonProperty("expires_in")
    private Integer expiresIn;

    private String scope;

    @JsonProperty("client_id")
    private String clientId;


    public TokenResponse(String accessToken, String tokenType, Integer expiresIn, String scope, String clientId) {
        this();
        this.accessToken = accessToken;
        this.tokenType = tokenType;
        this.expiresIn = expiresIn;
        this.scope = scope;
        this.clientId = clientId;
    }
}
