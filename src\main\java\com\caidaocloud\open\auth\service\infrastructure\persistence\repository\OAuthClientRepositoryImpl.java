package com.caidaocloud.open.auth.service.infrastructure.persistence.repository;

import com.caidaocloud.open.auth.service.domain.entity.OAuthClient;
import com.caidaocloud.open.auth.service.domain.repository.OAuthClientRepository;
import com.caidaocloud.open.auth.service.infrastructure.persistence.po.OAuthClientPo;
import com.caidaocloud.open.auth.service.infrastructure.persistence.mapper.OAuthClientMapper;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * OAuth Client Repository Implementation (Infrastructure Layer)
 * 
 * <AUTHOR> <PERSON>
 */
@Repository
public class OAuthClientRepositoryImpl implements OAuthClientRepository {

    @Autowired
    private OAuthClientMapper clientMapper;

    @Override
    public Optional<OAuthClient> findByClientId(String clientId) {
        Optional<OAuthClientPo> entity = clientMapper.findByClientId(clientId);
        return entity.map(this::toDomainModel);
    }

    @Override
    public OAuthClient save(OAuthClient client) {
        OAuthClientPo entity = toEntity(client);
        if (clientMapper.existsByClientId(client.getClientId())) {
            clientMapper.updateById(entity);
        } else {
            clientMapper.insert(entity);
        }
        return toDomainModel(entity);
    }

    @Override
    public void deleteByClientId(String clientId) {
        Optional<OAuthClientPo> entity = clientMapper.findByClientId(clientId);
        if (entity.isPresent()) {
            clientMapper.deleteById(entity.get().getClientId());
        }
    }

    @Override
    public List<OAuthClient> findAllEnabled() {
        List<OAuthClientPo> entities = clientMapper.findAllEnabled();
        return entities.stream()
                .map(this::toDomainModel)
                .collect(Collectors.toList());
    }

    @Override
    public boolean existsByClientId(String clientId) {
        return clientMapper.existsByClientId(clientId);
    }

    @Override
    public void updateLastUsed(String clientId) {
        clientMapper.updateLastUsed(clientId);
    }

    /**
     * Convert entity to domain model
     */
    private OAuthClient toDomainModel(OAuthClientPo entity) {
        OAuthClient client = new OAuthClient();
        client.setClientId(entity.getClientId());
        client.setClientSecret(entity.getClientSecret());
        client.setScope(entity.getScope());
        client.setAuthorities(entity.getAuthorities());
        client.setAccessTokenValidity(entity.getAccessTokenValidity());
        client.setClientName(entity.getClientName());
        client.setClientDescription(entity.getClientDescription());
        client.setEnabled(entity.getEnabled());
        client.setCreatedTime(entity.getCreatedTime());
        client.setUpdatedTime(entity.getUpdatedTime());
        return client;
    }

    /**
     * Convert domain model to entity
     */
    private OAuthClientPo toEntity(OAuthClient client) {
        OAuthClientPo entity = new OAuthClientPo();
        entity.setClientId(client.getClientId());
        entity.setClientSecret(client.getClientSecret());
        entity.setScope(client.getScope());
        entity.setAuthorities(client.getAuthorities());
        entity.setAccessTokenValidity(client.getAccessTokenValidity());
        entity.setClientName(client.getClientName());
        entity.setClientDescription(client.getClientDescription());
        entity.setEnabled(client.getEnabled());
        entity.setCreatedTime(client.getCreatedTime());
        entity.setUpdatedTime(client.getUpdatedTime());
        return entity;
    }
}
