package com.caidaocloud.open.auth.service.infrastructure.config.oauth;

import org.springframework.security.oauth2.common.DefaultOAuth2AccessToken;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.security.oauth2.provider.token.TokenEnhancer;

import java.util.HashMap;
import java.util.Map;

/**
 * Custom Token Enhancer for Client Credentials Grant
 * 
 * <AUTHOR>
 */
public class CustomTokenEnhancer implements TokenEnhancer {

    @Override
    public OAuth2AccessToken enhance(OAuth2AccessToken accessToken, OAuth2Authentication authentication) {
        Map<String, Object> additionalInfo = new HashMap<>();
        
        // Add timestamp
        additionalInfo.put("timestamp", System.currentTimeMillis());
        
        // Add issuer
        additionalInfo.put("iss", "caidao-auth-service");
        
        // Add grant type
        additionalInfo.put("grant_type", "client_credentials");
        
        // Add client information
        if (authentication.getOAuth2Request() != null) {
            additionalInfo.put("client_id", authentication.getOAuth2Request().getClientId());
            additionalInfo.put("scope", authentication.getOAuth2Request().getScope());
            additionalInfo.put("authorities", authentication.getOAuth2Request().getAuthorities());
        }
        
        // Add token type
        additionalInfo.put("token_type", "Bearer");
        
        ((DefaultOAuth2AccessToken) accessToken).setAdditionalInformation(additionalInfo);
        return accessToken;
    }
}
