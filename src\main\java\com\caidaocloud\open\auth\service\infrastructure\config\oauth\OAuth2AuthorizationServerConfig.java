package com.caidaocloud.open.auth.service.infrastructure.config.oauth;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.caidaocloud.open.auth.service.domain.entity.OAuthClient;
import com.caidaocloud.open.auth.service.infrastructure.config.aliyun.AliyunKmsJwtClientAuthConverter;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.security.oauth2.common.DefaultOAuth2AccessToken;
import org.springframework.security.oauth2.config.annotation.configurers.ClientDetailsServiceConfigurer;
import org.springframework.security.oauth2.config.annotation.web.configuration.AuthorizationServerConfigurerAdapter;
import org.springframework.security.oauth2.config.annotation.web.configuration.EnableAuthorizationServer;
import org.springframework.security.oauth2.config.annotation.web.configurers.AuthorizationServerEndpointsConfigurer;
import org.springframework.security.oauth2.config.annotation.web.configurers.AuthorizationServerSecurityConfigurer;
import org.springframework.security.oauth2.provider.token.DefaultTokenServices;
import org.springframework.security.oauth2.provider.token.TokenEnhancer;
import org.springframework.security.oauth2.provider.token.TokenEnhancerChain;
import org.springframework.security.oauth2.provider.token.TokenStore;
import org.springframework.security.oauth2.provider.token.store.JwtAccessTokenConverter;
import org.springframework.security.oauth2.provider.token.store.redis.RedisTokenStore;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;

/**
 * OAuth2 Authorization Server Configuration (Client Credentials Only)
 *
 * <AUTHOR> Zhou
 */
@Configuration
@EnableAuthorizationServer
public class OAuth2AuthorizationServerConfig extends AuthorizationServerConfigurerAdapter {

    /**
     * 统一配置认证请求前缀
     */
    @NacosValue(value = "${hrpaas.oauth.pathmapping.prefix:/api/auth/open/v1}", autoRefreshed = true)
    private String oauthPathPrefix;

    @Autowired
    private OAuthClientDetailService clientDetailsService;

    @Autowired
    private RedisConnectionFactory redisConnectionFactory;

    // TODO: 2025/8/26 改为bean注册，支持配置使用common或aliyun_kms
    @Value("${oauth2.jwt.signing-key}")
    private String jwtSigningKey;

    @Value("${oauth2.jwt.access-token-validity-seconds:600}")
    private int accessTokenValiditySeconds;

    @Resource
    private OAuthClientDetailService oAuthClientDetailService;

    @Resource
    private AliyunKmsJwtClientAuthConverter authConverter;


    /**
     * Configure client details service
     */
    @Override
    public void configure(ClientDetailsServiceConfigurer clients) throws Exception {
        clients.withClientDetails(clientDetailsService);
    }

    /**
     * Configure authorization server endpoints
     * Only support client_credentials grant type
     */
    @Override
    public void configure(AuthorizationServerEndpointsConfigurer endpoints) throws Exception {
        TokenEnhancerChain tokenEnhancerChain = new TokenEnhancerChain();
        tokenEnhancerChain.setTokenEnhancers(Arrays.asList(tokenEnhancer(), authConverter));

        endpoints
                .tokenStore(tokenStore())
                .tokenEnhancer(tokenEnhancerChain)
                .accessTokenConverter(authConverter)
                .tokenServices(tokenServices())
                .reuseRefreshTokens(false)
                .pathMapping("/oauth/authorize", oauthPathPrefix + "/oauth/authorize")
                // 修改客户端认证的默认 path
                .pathMapping("/oauth/token", oauthPathPrefix + "/oauth/token")
                .pathMapping("/oauth/confirm_access", oauthPathPrefix + "/oauth/confirm_access")
                .pathMapping("/oauth/error", oauthPathPrefix + "/oauth/error")
                .pathMapping("/oauth/check_token", oauthPathPrefix + "/oauth/check_token")
                .pathMapping("/oauth/token_key", oauthPathPrefix + "/oauth/token_key");
                // Only allow client_credentials grant type
                // .allowedTokenEndpointRequestMethods(org.springframework.http.HttpMethod.POST);
    }

    /**
     * Configure authorization server security
     */
    @Override
    public void configure(AuthorizationServerSecurityConfigurer security) throws Exception {
        security
                .tokenKeyAccess("permitAll()")
                .checkTokenAccess("isAuthenticated()")
                .allowFormAuthenticationForClients();
    }

    /**
     * Token store configuration using Redis
     */
    @Bean
    public TokenStore tokenStore() {
        return new RedisTokenStore(redisConnectionFactory);
    }

    /**
     * JWT Access Token Converter
     */
    @Bean
    public JwtAccessTokenConverter jwtAccessTokenConverter() {
        JwtAccessTokenConverter converter = new JwtAccessTokenConverter();
        converter.setSigningKey(jwtSigningKey);
        return converter;
    }

    /**
     * Custom Token Enhancer for client credentials
     */
    @Bean
    public TokenEnhancer tokenEnhancer() {
        return (accessToken, authentication) -> {
            final Map<String, Object> additionalInfo = new HashMap<>(10);
            additionalInfo.put("userid", 0);
            additionalInfo.put("empid", 0);
            if (authentication.getUserAuthentication() != null) {
                // UserDetailDto user = (UserDetailDto) authentication.getUserAuthentication().getPrincipal();
                // if (user != null) {
                //     additionalInfo.put("tenantId", user.getTenantId());
                //     additionalInfo.put("belongId", user.getBelongId());
                // }
            } else {
                OAuthClient oauthClient = oAuthClientDetailService.loadClientById(authentication.getOAuth2Request().getClientId());
                additionalInfo.put("tenantId", oauthClient.getClientId());
                // additionalInfo.put("authUrl", StringUtils.isNotBlank(oauthClient.getAuthUrl()) ? oauthClient.getAuthUrl() : "");
            }
            ((DefaultOAuth2AccessToken) accessToken).setAdditionalInformation(additionalInfo);
            return accessToken;
        };
    }

    /**
     * Token Services Configuration
     */
    @Bean
    @Primary
    public DefaultTokenServices tokenServices() {
        DefaultTokenServices tokenServices = new DefaultTokenServices();
        tokenServices.setTokenStore(tokenStore());
        tokenServices.setSupportRefreshToken(true);
        tokenServices.setReuseRefreshToken(false);
        tokenServices.setAccessTokenValiditySeconds(accessTokenValiditySeconds);
        tokenServices.setTokenEnhancer(tokenEnhancer());
        return tokenServices;
    }
}
