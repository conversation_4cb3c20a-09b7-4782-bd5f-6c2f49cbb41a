package com.caidaocloud.open.auth.service.infrastructure.config.jwt;

import io.jsonwebtoken.JwsHeader;

import java.util.Map;

/**
 * JWT签名服务接口
 * 支持多种签名方式：阿里云KMS、简单对称加密等
 *
 * <AUTHOR>
 */
public interface JwtSignerService {

    /**
     * 对JWT载荷进行签名
     *
     * @param claims JWT载荷数据
     * @return 签名后的JWT字符串
     */
    String sign(Map<String, Object> claims);

    /**
     * 验证JWT签名
     *
     * @param header JWT头部信息
     * @param payload JWT载荷（Base64编码）
     * @param signature JWT签名
     * @return 验证结果，true表示验证通过
     */
    boolean verify(JwsHeader header, String payload, String signature);

    /**
     * 获取签名方式类型
     *
     * @return 签名方式类型标识
     */
    String getSignerType();
}
