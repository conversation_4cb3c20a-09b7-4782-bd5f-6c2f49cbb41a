package com.caidaocloud.open.auth.service.application.service;

import com.caidaocloud.open.auth.service.application.dto.ClientCredentialsRequest;
import com.caidaocloud.open.auth.service.application.dto.TokenResponse;
import com.caidaocloud.open.auth.service.domain.entity.OAuthClient;
import com.caidaocloud.open.auth.service.domain.service.OAuthClientDomainService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.provider.TokenRequest;
import org.springframework.security.oauth2.provider.client.ClientCredentialsTokenGranter;
import org.springframework.security.oauth2.provider.token.TokenStore;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashSet;
import java.util.Set;

/**
 * OAuth Application Service Implementation
 * 
 * <AUTHOR>
 */
@Service
public class OAuthApplicationService {

    private static final Logger logger = LoggerFactory.getLogger(OAuthApplicationService.class);

    @Autowired
    private OAuthClientDomainService clientDomainService;

    @Autowired
    private TokenStore tokenStore;

    @Autowired
    private ClientCredentialsTokenGranter tokenGranter;

    public TokenResponse handleClientCredentials(ClientCredentialsRequest request) {
        try {
            // Validate grant type
            if (!"client_credentials".equals(request.getGrantType())) {
                throw new IllegalArgumentException("Unsupported grant type: " + request.getGrantType());
            }

            // Validate client credentials
            if (!clientDomainService.validateClientCredentials(request.getClientId(), request.getClientSecret())) {
                throw new IllegalArgumentException("Invalid client credentials");
            }

            // Validate scope if provided
            if (request.getScope() != null && !request.getScope().isEmpty()) {
                if (!clientDomainService.validateScope(request.getClientId(), request.getScope())) {
                    throw new IllegalArgumentException("Invalid scope: " + request.getScope());
                }
            }

            // Get client details
            OAuthClient client = clientDomainService.getClient(request.getClientId());
            if (client == null) {
                throw new IllegalArgumentException("Client not found");
            }

            // Create token request
            Set<String> scopes = new HashSet<>();
            if (request.getScope() != null && !request.getScope().isEmpty()) {
                Collections.addAll(scopes, request.getScope().split(","));
            } else if (client.getScope() != null) {
                Collections.addAll(scopes, client.getScope().split(","));
            }

            TokenRequest tokenRequest = new TokenRequest(
                Collections.emptyMap(),
                request.getClientId(),
                scopes,
                request.getGrantType()
            );

            // Generate token
            OAuth2AccessToken accessToken = tokenGranter.grant(request.getGrantType(), tokenRequest);
            
            if (accessToken == null) {
                throw new RuntimeException("Failed to generate access token");
            }

            // Record client usage
            clientDomainService.recordClientUsage(request.getClientId());

            // Create response
            TokenResponse response = new TokenResponse(
                accessToken.getValue(),
                accessToken.getTokenType(),
                accessToken.getExpiresIn(),
                String.join(",", scopes),
                request.getClientId()
            );

            logger.info("Access token generated successfully for client: {}", request.getClientId());
            return response;

        } catch (Exception e) {
            logger.error("Failed to handle client credentials request for client: {}", request.getClientId(), e);
            throw new RuntimeException("Token generation failed: " + e.getMessage(), e);
        }
    }

    public boolean validateToken(String token) {
        try {
            OAuth2AccessToken accessToken = tokenStore.readAccessToken(token);
            return accessToken != null && !accessToken.isExpired();
        } catch (Exception e) {
            logger.warn("Token validation failed: {}", e.getMessage());
            return false;
        }
    }

    public void revokeToken(String token) {
        try {
            OAuth2AccessToken accessToken = tokenStore.readAccessToken(token);
            if (accessToken != null) {
                tokenStore.removeAccessToken(accessToken);
                logger.info("Token revoked successfully");
            }
        } catch (Exception e) {
            logger.error("Failed to revoke token", e);
            throw new RuntimeException("Token revocation failed: " + e.getMessage(), e);
        }
    }


    public void updateClientStatus(String clientId, boolean enabled) {
        try {
            clientDomainService.updateClientStatus(clientId, enabled);
        } catch (Exception e) {
            logger.error("Failed to update client status: {}", clientId, e);
            throw new RuntimeException("Client status update failed: " + e.getMessage(), e);
        }
    }
}
