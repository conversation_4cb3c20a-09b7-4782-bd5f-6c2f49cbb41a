<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="68c38bb3-e8dd-42d5-bc8a-ea15818f8550" name="Changes" comment="项目初始化">
      <change afterPath="$PROJECT_DIR$/.gitignore" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/README.md" beforeDir="false" afterPath="$PROJECT_DIR$/README.md" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ChangesViewManager">
    <option name="groupingKeys">
      <option value="directory" />
    </option>
  </component>
  <component name="CodeStyleSettingsInfer">
    <option name="done" value="true" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="master" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="userSettingsFile" value="C:\mvn\setting_caidao\settings-new.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectCodeStyleSettingsMigration">
    <option name="version" value="2" />
  </component>
  <component name="ProjectId" id="31UpGRhXFMaDwrJPX5ZzqpqfCoG" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">
    <property name="RunOnceActivity.OpenProjectViewOnStart" value="true" />
    <property name="RunOnceActivity.ShowReadmeOnStart" value="true" />
    <property name="WebServerToolWindowFactoryState" value="false" />
    <property name="last_opened_file_path" value="$PROJECT_DIR$/pom.xml" />
    <property name="settings.editor.selected.configurable" value="MavenSettings" />
    <property name="vue.rearranger.settings.migration" value="true" />
  </component>
  <component name="RecentsManager">
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.caidaocloud.open.auth.service.infrastructure.config.oauth" />
    </key>
  </component>
  <component name="RunManager" selected="Spring Boot.CaidaoAuthApplication">
    <configuration default="true" type="DjangoTestsConfigurationType">
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <module name="" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="TARGET" value="" />
      <option name="SETTINGS_FILE" value="" />
      <option name="CUSTOM_SETTINGS" value="false" />
      <option name="USE_OPTIONS" value="false" />
      <option name="OPTIONS" value="" />
      <method v="2" />
    </configuration>
    <configuration default="true" type="JetRunConfigurationType">
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="KotlinStandaloneScriptRunConfigurationType">
      <option name="filePath" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="PyBehaveRunConfigurationType" factoryName="Behave">
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <module name="" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="ADDITIONAL_ARGS" value="" />
      <method v="2" />
    </configuration>
    <configuration default="true" type="PythonConfigurationType" factoryName="Python">
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <module name="" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration default="true" type="Python.FlaskServer">
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <module name="" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="launchJavascriptDebuger" value="false" />
      <method v="2" />
    </configuration>
    <configuration name="CaidaoAuthApplication (1)" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <module name="caidao-open-auth-service" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.caidaocloud.open.auth.service.OpenAuthApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="CaidaoAuthApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot">
      <module name="caidao-open-auth-service" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.caidao.auth.CaidaoAuthApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="Tox" factoryName="Tox">
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <method v="2" />
    </configuration>
    <configuration default="true" type="tests" factoryName="Doctests">
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="" />
      <option name="IS_MODULE_SDK" value="false" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <module name="" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="" />
      <option name="CLASS_NAME" value="" />
      <option name="METHOD_NAME" value="" />
      <option name="FOLDER_NAME" value="" />
      <option name="TEST_TYPE" value="TEST_SCRIPT" />
      <option name="PATTERN" value="" />
      <option name="USE_PATTERN" value="false" />
      <method v="2" />
    </configuration>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="68c38bb3-e8dd-42d5-bc8a-ea15818f8550" name="Changes" comment="" />
      <created>1755588414410</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1755588414410</updated>
      <workItem from="1755588417337" duration="7280000" />
      <workItem from="1756102526750" duration="10760000" />
      <workItem from="1756177163520" duration="13335000" />
      <workItem from="1756360677547" duration="5950000" />
    </task>
    <task id="LOCAL-00001" summary="init">
      <created>1755589546195</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1755589546196</updated>
    </task>
    <task id="LOCAL-00002" summary="项目初始化">
      <created>1755589691263</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1755589691263</updated>
    </task>
    <task id="LOCAL-00003" summary="项目初始化">
      <created>1755590547219</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1755590547219</updated>
    </task>
    <task id="LOCAL-00004" summary="项目初始化">
      <created>1755600229680</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1755600229680</updated>
    </task>
    <task id="LOCAL-00005" summary="项目初始化">
      <created>1756179674365</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1756179674365</updated>
    </task>
    <task id="LOCAL-00006" summary="项目初始化">
      <created>1756207494283</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1756207494284</updated>
    </task>
    <task id="LOCAL-00007" summary="项目初始化">
      <created>1756366998557</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1756366998557</updated>
    </task>
    <option name="localTasksCounter" value="8" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
    <option name="oldMeFiltersMigrated" value="true" />
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="init" />
    <MESSAGE value="项目初始化" />
    <option name="LAST_COMMIT_MESSAGE" value="项目初始化" />
  </component>
</project>