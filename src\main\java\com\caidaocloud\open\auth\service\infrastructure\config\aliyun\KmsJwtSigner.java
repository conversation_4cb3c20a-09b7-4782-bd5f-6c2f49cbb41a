package com.caidaocloud.open.auth.service.infrastructure.config.aliyun;

import java.util.Base64;
import java.util.Map;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.aliyun.kms20160120.Client;
import com.aliyun.kms20160120.models.AsymmetricSignRequest;
import com.aliyun.kms20160120.models.AsymmetricSignResponse;
import com.aliyun.kms20160120.models.AsymmetricVerifyResponse;
import com.aliyun.tea.TeaException;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.models.RuntimeOptions;
import com.caidaocloud.open.auth.service.infrastructure.config.jwt.JwtSignerService;
import com.caidaocloud.util.FastjsonUtil;
import io.jsonwebtoken.JwsHeader;
import lombok.extern.slf4j.Slf4j;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@ConditionalOnProperty(name = "jwt.signer.type", havingValue = "aliyun-kms")
public class KmsJwtSigner implements JwtSignerService {

    private final Client kmsClient;
    @NacosValue("${aliyun.kms.key-id}")
    private String keyId;
    @NacosValue("${aliyun.kms.key-version-id}")
    private String keyVersionId;
    @NacosValue("${aliyun.kms.alg}")
    private String alg;

    public KmsJwtSigner(AliyunConfig aliyunConfig) throws Exception {
        Config config = new Config()
                .setAccessKeyId(aliyunConfig.getAccessKeyId())
                .setAccessKeySecret(aliyunConfig.getAccessKeySecret())
                .setEndpoint(aliyunConfig.getEndpoint());
        this.kmsClient = new Client(config);
    }

    /**
     * 非对称签名
     * 
     * @param digest
     */
    private String sign(String digest) {
        AsymmetricSignRequest asymmetricSignRequest = new AsymmetricSignRequest()
                .setKeyId(keyId)
                .setKeyVersionId(keyVersionId)
                .setAlgorithm(alg)
                .setDigest(digest);
        RuntimeOptions runtime = new RuntimeOptions();
        try {
            // 复制代码运行请自行打印 API 的返回值
            AsymmetricSignResponse response = kmsClient.asymmetricSignWithOptions(asymmetricSignRequest, runtime);
            return response.getBody().value;
        } catch (TeaException error) {
            // TODO: 2025/8/28 错误日志记录
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
            throw error;
        } catch (Exception _error) {
            TeaException error = new TeaException(_error.getMessage(), _error);
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
            throw error;
        }
    }

    private boolean verify(String keyId, String keyVersionId, String alg, String digest, String sign) {
        com.aliyun.kms20160120.models.AsymmetricVerifyRequest asymmetricVerifyRequest = new com.aliyun.kms20160120.models.AsymmetricVerifyRequest()
                .setKeyId(keyId)
                .setKeyVersionId(keyVersionId)
                .setAlgorithm(alg)
                .setDigest(digest)
                .setValue(sign);
        RuntimeOptions runtime = new RuntimeOptions();
        try {
            AsymmetricVerifyResponse response = kmsClient.asymmetricVerifyWithOptions(asymmetricVerifyRequest, runtime);
            return response.getBody().value;
        } catch (TeaException error) {
            // TODO: 2025/8/28 错误日志记录
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
            throw error;
        } catch (Exception _error) {
            TeaException error = new TeaException(_error.getMessage(), _error);
            // 此处仅做打印展示，请谨慎对待异常处理，在工程项目中切勿直接忽略异常。
            // 错误 message
            System.out.println(error.getMessage());
            // 诊断地址
            System.out.println(error.getData().get("Recommend"));
            com.aliyun.teautil.Common.assertAsString(error.message);
            throw error;
        }
    }

    public String sign(Map<String, Object> claims) {
        String json = FastjsonUtil.toJson(claims);
        String payload = Base64.getUrlEncoder().withoutPadding().encodeToString(json.getBytes());
        return sign(payload);
    }

    public boolean verify(JwsHeader header, String payload, String signature) {
        String keyId = header.getKeyId();
        String algorithm = header.getAlgorithm();
        String keyVersionId = (String) header.get("keyVersionId");
        return verify(keyId, keyVersionId, algorithm, payload, signature);
    }

    @Override
    public String getSignerType() {
        return "aliyun-kms";
    }
}