package com.caidaocloud.open.auth.service.domain.entity;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * OAuth Client Domain Model
 * 
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class OAuthClient {

    private String clientId;
    private String tenantId;
    private String clientSecret;
    private String scope;
    private String authorities;
    private Integer accessTokenValidity;
    private Integer refreshTokenValidity;
    private String authorizedGrantTypes = "";
    private String clientName;
    private String clientDescription;
    private Boolean enabled;
    private LocalDateTime createdTime;
    private LocalDateTime updatedTime;

    public OAuthClient(String clientId, String clientSecret, String scope) {
        this.clientId = clientId;
        this.clientSecret = clientSecret;
        this.scope = scope;
        this.enabled = true;
        this.accessTokenValidity = 3600; // 1 hour default
        this.createdTime = LocalDateTime.now();
        this.updatedTime = LocalDateTime.now();
    }

    // Business methods
    public boolean isEnabled() {
        return enabled != null && enabled;
    }

    public boolean hasScope(String requestedScope) {
        if (scope == null || scope.isEmpty()) {
            return false;
        }
        List<String> scopes = Arrays.asList(scope.split(","));
        return scopes.contains(requestedScope);
    }

    public boolean isValidForClientCredentials() {
        return isEnabled() && clientSecret != null && !clientSecret.isEmpty();
    }

    public void updateLastUsed() {
        this.updatedTime = LocalDateTime.now();
    }

}
