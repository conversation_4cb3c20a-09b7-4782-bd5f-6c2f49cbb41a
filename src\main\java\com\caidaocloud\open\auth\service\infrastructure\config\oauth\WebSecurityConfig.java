package com.caidaocloud.open.auth.service.infrastructure.config.oauth;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.builders.WebSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;

/**
 * Web Security Configuration (Simplified for Client Credentials Only)
 * 
 * <AUTHOR>
 */
@Configuration
@EnableWebSecurity
public class WebSecurityConfig extends WebSecurityConfigurerAdapter {

    /**
     * Password encoder configuration
     */
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder(12);
    }

    /**
     * Configure HTTP security
     */
    @Override
    protected void configure(HttpSecurity http) throws Exception {
        http
                .csrf().disable()
                .authorizeRequests()
                // OAuth2 token endpoint
                .antMatchers("/oauth/token", "/oauth/token_key", "/oauth/check_token",
                        "/oauth/authorize", "/oauth/confirm_access", "/oauth/error").permitAll()
                .anyRequest().authenticated()
                .and()
                .httpBasic(); // Basic auth for client credentials
    }
}
