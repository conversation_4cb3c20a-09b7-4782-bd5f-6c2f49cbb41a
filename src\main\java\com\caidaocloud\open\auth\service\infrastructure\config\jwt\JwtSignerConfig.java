package com.caidaocloud.open.auth.service.infrastructure.config.jwt;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.util.List;

/**
 * JWT签名服务配置类
 * 根据配置选择使用的JWT签名实现
 *
 * <AUTHOR>
 */
@Configuration
@Slf4j
public class JwtSignerConfig {

    @Value("${jwt.signer.type:simple}")
    private String signerType;

    @Autowired
    private List<JwtSignerService> jwtSignerServices;

    /**
     * 根据配置选择JWT签名服务实现
     *
     * @return JWT签名服务实例
     */
    @Bean
    @Primary
    public JwtSignerService jwtSignerService() {
        for (JwtSignerService service : jwtSignerServices) {
            if (signerType.equals(service.getSignerType())) {
                log.info("Selected JWT signer: {}", service.getSignerType());
                return service;
            }
        }
        
        // 如果没有找到匹配的实现，默认使用第一个可用的
        if (!jwtSignerServices.isEmpty()) {
            JwtSignerService defaultService = jwtSignerServices.get(0);
            log.warn("JWT signer type '{}' not found, using default: {}", 
                    signerType, defaultService.getSignerType());
            return defaultService;
        }
        
        throw new IllegalStateException("No JWT signer service implementation found");
    }
}
