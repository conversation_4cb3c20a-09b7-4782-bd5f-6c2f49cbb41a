package com.caidaocloud.open.auth.service.domain.service;

import com.caidaocloud.open.auth.service.domain.entity.OAuthClient;
import com.caidaocloud.open.auth.service.domain.repository.OAuthClientRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Optional;

/**
 * OAuth Client Domain Service Implementation
 * 
 * <AUTHOR>
 */
@Service
public class OAuthClientDomainService {

    private static final Logger logger = LoggerFactory.getLogger(OAuthClientDomainService.class);

    @Autowired
    private OAuthClientRepository clientRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    public boolean validateClientCredentials(String clientId, String clientSecret) {
        Optional<OAuthClient> clientOpt = clientRepository.findByClientId(clientId);

        if (!clientOpt.isPresent()) {
            logger.warn("Client not found: {}", clientId);
            return false;
        }

        OAuthClient client = clientOpt.get();

        if (!client.isValidForClientCredentials()) {
            logger.warn("Client not valid for client credentials: {}", clientId);
            return false;
        }

        boolean isValid = passwordEncoder.matches(clientSecret, client.getClientSecret());

        if (isValid) {
            logger.debug("Client credentials validated successfully: {}", clientId);
            recordClientUsage(clientId);
        } else {
            logger.warn("Invalid client credentials: {}", clientId);
        }

        return isValid;
    }

    public boolean validateScope(String clientId, String requestedScope) {
        Optional<OAuthClient> clientOpt = clientRepository.findByClientId(clientId);

        if (!clientOpt.isPresent()) {
            return false;
        }

        OAuthClient client = clientOpt.get();
        return client.hasScope(requestedScope);
    }

    public OAuthClient getClient(String clientId) {
        return clientRepository.findByClientId(clientId).orElse(null);
    }

    public OAuthClient createClient(String clientId, String clientSecret, String scope, String clientName) {
        if (clientRepository.existsByClientId(clientId)) {
            throw new IllegalArgumentException("Client already exists: " + clientId);
        }

        OAuthClient client = new OAuthClient(clientId, passwordEncoder.encode(clientSecret), scope);
        client.setClientName(clientName);
        client.setAuthorities("ROLE_CLIENT");

        OAuthClient savedClient = clientRepository.save(client);
        logger.info("Client created successfully: {}", clientId);

        return savedClient;
    }

    public OAuthClient updateClient(OAuthClient client) {
        if (!clientRepository.existsByClientId(client.getClientId())) {
            throw new IllegalArgumentException("Client not found: " + client.getClientId());
        }

        client.setUpdatedTime(LocalDateTime.now());
        OAuthClient updatedClient = clientRepository.save(client);
        logger.info("Client updated successfully: {}", client.getClientId());

        return updatedClient;
    }

    public void updateClientStatus(String clientId, boolean enabled) {
        Optional<OAuthClient> clientOpt = clientRepository.findByClientId(clientId);

        if (!clientOpt.isPresent()) {
            throw new IllegalArgumentException("Client not found: " + clientId);
        }

        OAuthClient client = clientOpt.get();
        client.setEnabled(enabled);
        client.setUpdatedTime(LocalDateTime.now());

        clientRepository.save(client);
        logger.info("Client status updated: {} -> {}", clientId, enabled);
    }

    public void recordClientUsage(String clientId) {
        try {
            clientRepository.updateLastUsed(clientId);
            logger.debug("Client usage recorded: {}", clientId);
        } catch (Exception e) {
            logger.warn("Failed to record client usage: {}", clientId, e);
        }
    }

    public java.util.List<OAuthClient> getAllEnabledClients() {
        return clientRepository.findAllEnabled();
    }

    public void deleteClient(String clientId) {
        if (!clientRepository.existsByClientId(clientId)) {
            throw new IllegalArgumentException("Client not found: " + clientId);
        }

        clientRepository.deleteByClientId(clientId);
        logger.info("Client deleted successfully: {}", clientId);
    }
}
