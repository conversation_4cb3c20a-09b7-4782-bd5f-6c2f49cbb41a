package com.caidaocloud.open.auth.service.infrastructure.persistence.po;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * OAuth Client po
 * 
 * <AUTHOR>
 */
@TableName("oauth_client_details")
@Data
public class OAuthClientPo {

    @TableId
    private String clientId;

    private String clientSecret;

    private String scope;

    private String authorities;

    private Integer accessTokenValidity;

    private String clientName;

    private String clientDescription;

    private Boolean enabled = true;

    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createdTime;

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedTime;

    @TableLogic
    private Boolean deleted = false;

    // Constructors
    public OAuthClientPo() {
    }

    public OAuthClientPo(String clientId, String clientSecret, String scope) {
        this.clientId = clientId;
        this.clientSecret = clientSecret;
        this.scope = scope;
        this.enabled = true;
        this.accessTokenValidity = 3600;
        this.createdTime = LocalDateTime.now();
        this.updatedTime = LocalDateTime.now();
    }
}
