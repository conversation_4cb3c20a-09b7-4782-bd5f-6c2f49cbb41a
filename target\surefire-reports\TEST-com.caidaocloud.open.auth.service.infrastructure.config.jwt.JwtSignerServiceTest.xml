<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report.xsd" name="com.caidaocloud.open.auth.service.infrastructure.config.jwt.JwtSignerServiceTest" time="2.042" tests="1" errors="0" skipped="0" failures="0">
  <properties>
    <property name="sun.desktop" value="windows"/>
    <property name="awt.toolkit" value="sun.awt.windows.WToolkit"/>
    <property name="file.encoding.pkg" value="sun.io"/>
    <property name="java.specification.version" value="1.8"/>
    <property name="sun.cpu.isalist" value="amd64"/>
    <property name="sun.jnu.encoding" value="GBK"/>
    <property name="java.class.path" value="C:\caidao\caidao_open_auth_service\target\test-classes;C:\caidao\caidao_open_auth_service\target\classes;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-web\2.1.0.RELEASE\spring-boot-starter-web-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter\2.1.0.RELEASE\spring-boot-starter-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot\2.1.0.RELEASE\spring-boot-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-logging\2.1.0.RELEASE\spring-boot-starter-logging-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-classic\1.2.11\logback-classic-1.2.11.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-core\1.2.11\logback-core-1.2.11.jar;C:\Users\<USER>\.m2\repository\org\slf4j\jul-to-slf4j\1.7.25\jul-to-slf4j-1.7.25.jar;C:\Users\<USER>\.m2\repository\javax\annotation\javax.annotation-api\1.3.2\javax.annotation-api-1.3.2.jar;C:\Users\<USER>\.m2\repository\org\yaml\snakeyaml\1.33\snakeyaml-1.33.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-json\2.1.0.RELEASE\spring-boot-starter-json-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.13.3\jackson-datatype-jdk8-2.13.3.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.13.3\jackson-datatype-jsr310-2.13.3.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.13.3\jackson-module-parameter-names-2.13.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-tomcat\2.1.0.RELEASE\spring-boot-starter-tomcat-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-el\9.0.12\tomcat-embed-el-9.0.12.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-websocket\9.0.12\tomcat-embed-websocket-9.0.12.jar;C:\Users\<USER>\.m2\repository\org\hibernate\validator\hibernate-validator\6.0.13.Final\hibernate-validator-6.0.13.Final.jar;C:\Users\<USER>\.m2\repository\javax\validation\validation-api\2.0.1.Final\validation-api-2.0.1.Final.jar;C:\Users\<USER>\.m2\repository\org\jboss\logging\jboss-logging\3.3.2.Final\jboss-logging-3.3.2.Final.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-web\5.1.2.RELEASE\spring-web-5.1.2.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-webmvc\5.1.2.RELEASE\spring-webmvc-5.1.2.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-expression\5.1.2.RELEASE\spring-expression-5.1.2.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-security\2.1.0.RELEASE\spring-boot-starter-security-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-aop\5.1.2.RELEASE\spring-aop-5.1.2.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-config\5.1.1.RELEASE\spring-security-config-5.1.1.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-web\5.1.1.RELEASE\spring-security-web-5.1.1.RELEASE.jar;C:\Users\<USER>\.m2\repository\com\caidaocloud\caidaocloud-commons\1.0.4-SNAPSHOT\caidaocloud-commons-1.0.4-SNAPSHOT.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-lang3\3.8.1\commons-lang3-3.8.1.jar;C:\Users\<USER>\.m2\repository\org\projectlombok\lombok\1.18.2\lombok-1.18.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context\5.1.2.RELEASE\spring-context-5.1.2.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-core\9.0.12\tomcat-embed-core-9.0.12.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\tomcat-annotations-api\9.0.12\tomcat-annotations-api-9.0.12.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-autoconfigure\2.1.0.RELEASE\spring-boot-autoconfigure-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\slf4j\slf4j-api\1.7.25\slf4j-api-1.7.25.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-swagger-ui\2.7.0\springfox-swagger-ui-2.7.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-configuration-processor\2.1.0.RELEASE\spring-boot-configuration-processor-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\com\squareup\okhttp3\okhttp\4.10.0\okhttp-4.10.0.jar;C:\Users\<USER>\.m2\repository\com\squareup\okio\okio-jvm\3.0.0\okio-jvm-3.0.0.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\kotlin\kotlin-stdlib\1.2.71\kotlin-stdlib-1.2.71.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\kotlin\kotlin-stdlib-common\1.2.71\kotlin-stdlib-common-1.2.71.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\annotations\13.0\annotations-13.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-mail\2.1.0.RELEASE\spring-boot-starter-mail-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context-support\5.1.2.RELEASE\spring-context-support-5.1.2.RELEASE.jar;C:\Users\<USER>\.m2\repository\com\sun\mail\javax.mail\1.6.2\javax.mail-1.6.2.jar;C:\Users\<USER>\.m2\repository\javax\activation\activation\1.1\activation-1.1.jar;C:\Users\<USER>\.m2\repository\commons-beanutils\commons-beanutils\1.9.4\commons-beanutils-1.9.4.jar;C:\Users\<USER>\.m2\repository\commons-logging\commons-logging\1.2\commons-logging-1.2.jar;C:\Users\<USER>\.m2\repository\commons-collections\commons-collections\3.2.2\commons-collections-3.2.2.jar;C:\Users\<USER>\.m2\repository\com\alibaba\fastjson\1.2.83\fastjson-1.2.83.jar;C:\Users\<USER>\.m2\repository\com\caidaocloud\metadata-sdk\1.1.5-SNAPSHOT\metadata-sdk-1.1.5-SNAPSHOT.jar;C:\Users\<USER>\.m2\repository\io\github\openfeign\feign-httpclient\10.1.0\feign-httpclient-10.1.0.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpclient\4.5.6\httpclient-4.5.6.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpcore\4.4.10\httpcore-4.4.10.jar;C:\Users\<USER>\.m2\repository\com\caidaocloud\paas-core\1.1.5-SNAPSHOT\paas-core-1.1.5-SNAPSHOT.jar;C:\Users\<USER>\.m2\repository\com\caidaocloud\galaxy-service-security\2.0.0-SNAPSHOT\galaxy-service-security-2.0.0-SNAPSHOT.jar;C:\Users\<USER>\.m2\repository\com\caidaocloud\galaxy-service-cache\2.0.0-SNAPSHOT\galaxy-service-cache-2.0.0-SNAPSHOT.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-data-redis\2.1.0.RELEASE\spring-boot-starter-data-redis-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-redis\2.1.2.RELEASE\spring-data-redis-2.1.2.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-keyvalue\2.1.2.RELEASE\spring-data-keyvalue-2.1.2.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-commons\2.1.2.RELEASE\spring-data-commons-2.1.2.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-oxm\5.1.2.RELEASE\spring-oxm-5.1.2.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\kotlin\kotlin-stdlib-jdk8\1.2.71\kotlin-stdlib-jdk8-1.2.71.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\kotlin\kotlin-stdlib-jdk7\1.2.71\kotlin-stdlib-jdk7-1.2.71.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\kotlin\kotlin-reflect\1.2.71\kotlin-reflect-1.2.71.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\module\jackson-module-kotlin\2.13.3\jackson-module-kotlin-2.13.3.jar;C:\Users\<USER>\.m2\repository\com\github\qiujiayu\autoload-cache-spring-boot-starter\7.0.8\autoload-cache-spring-boot-starter-7.0.8.jar;C:\Users\<USER>\.m2\repository\com\github\qiujiayu\autoload-cache\7.0.8\autoload-cache-7.0.8.jar;C:\Users\<USER>\.m2\repository\io\lettuce\lettuce-core\5.1.2.RELEASE\lettuce-core-5.1.2.RELEASE.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\reactor-core\3.2.2.RELEASE\reactor-core-3.2.2.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\reactivestreams\reactive-streams\1.0.2\reactive-streams-1.0.2.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-common\4.1.29.Final\netty-common-4.1.29.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport\4.1.29.Final\netty-transport-4.1.29.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-buffer\4.1.29.Final\netty-buffer-4.1.29.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver\4.1.29.Final\netty-resolver-4.1.29.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-handler\4.1.29.Final\netty-handler-4.1.29.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec\4.1.29.Final\netty-codec-4.1.29.Final.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-pool2\2.6.0\commons-pool2-2.6.0.jar;C:\Users\<USER>\.m2\repository\com\googlecode\totallylazy\totallylazy\2.286\totallylazy-2.286.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\oauth\spring-security-oauth2\2.3.4.RELEASE\spring-security-oauth2-2.3.4.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-beans\5.1.2.RELEASE\spring-beans-5.1.2.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-core\5.1.2.RELEASE\spring-core-5.1.2.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jcl\5.1.2.RELEASE\spring-jcl-5.1.2.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-core\5.1.1.RELEASE\spring-security-core-5.1.1.RELEASE.jar;C:\Users\<USER>\.m2\repository\commons-codec\commons-codec\1.11\commons-codec-1.11.jar;C:\Users\<USER>\.m2\repository\org\codehaus\jackson\jackson-mapper-asl\1.9.13\jackson-mapper-asl-1.9.13.jar;C:\Users\<USER>\.m2\repository\org\codehaus\jackson\jackson-core-asl\1.9.13\jackson-core-asl-1.9.13.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-jwt\1.0.11.RELEASE\spring-security-jwt-1.0.11.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\bouncycastle\bcpkix-jdk15on\1.64\bcpkix-jdk15on-1.64.jar;C:\Users\<USER>\.m2\repository\org\bouncycastle\bcprov-jdk15on\1.64\bcprov-jdk15on-1.64.jar;C:\Users\<USER>\.m2\repository\mysql\mysql-connector-java\8.0.20\mysql-connector-java-8.0.20.jar;C:\Users\<USER>\.m2\repository\com\zaxxer\HikariCP\3.2.0\HikariCP-3.2.0.jar;C:\Users\<USER>\.m2\repository\com\baomidou\mybatis-plus-boot-starter\3.5.1\mybatis-plus-boot-starter-3.5.1.jar;C:\Users\<USER>\.m2\repository\com\baomidou\mybatis-plus\3.5.1\mybatis-plus-3.5.1.jar;C:\Users\<USER>\.m2\repository\com\baomidou\mybatis-plus-extension\3.5.1\mybatis-plus-extension-3.5.1.jar;C:\Users\<USER>\.m2\repository\com\baomidou\mybatis-plus-core\3.5.1\mybatis-plus-core-3.5.1.jar;C:\Users\<USER>\.m2\repository\com\baomidou\mybatis-plus-annotation\3.5.1\mybatis-plus-annotation-3.5.1.jar;C:\Users\<USER>\.m2\repository\com\github\jsqlparser\jsqlparser\4.3\jsqlparser-4.3.jar;C:\Users\<USER>\.m2\repository\org\mybatis\mybatis\3.5.9\mybatis-3.5.9.jar;C:\Users\<USER>\.m2\repository\org\mybatis\mybatis-spring\2.0.6\mybatis-spring-2.0.6.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-jdbc\2.1.0.RELEASE\spring-boot-starter-jdbc-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jdbc\5.1.2.RELEASE\spring-jdbc-5.1.2.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-tx\5.1.2.RELEASE\spring-tx-5.1.2.RELEASE.jar;C:\Users\<USER>\.m2\repository\com\aliyun\kms20160120\1.2.3\kms20160120-1.2.3.jar;C:\Users\<USER>\.m2\repository\com\aliyun\tea-util\0.2.23\tea-util-0.2.23.jar;C:\Users\<USER>\.m2\repository\com\aliyun\endpoint-util\0.0.7\endpoint-util-0.0.7.jar;C:\Users\<USER>\.m2\repository\com\aliyun\openapiutil\0.2.1\openapiutil-0.2.1.jar;C:\Users\<USER>\.m2\repository\com\aliyun\alibabacloud-gateway-pop\0.0.6\alibabacloud-gateway-pop-0.0.6.jar;C:\Users\<USER>\.m2\repository\com\aliyun\darabonba-encode-util\0.0.2\darabonba-encode-util-0.0.2.jar;C:\Users\<USER>\.m2\repository\com\aliyun\darabonba-signature-util\0.0.4\darabonba-signature-util-0.0.4.jar;C:\Users\<USER>\.m2\repository\com\aliyun\darabonba-string\0.0.5\darabonba-string-0.0.5.jar;C:\Users\<USER>\.m2\repository\com\aliyun\darabonba-map\0.0.1\darabonba-map-0.0.1.jar;C:\Users\<USER>\.m2\repository\com\aliyun\darabonba-array\0.1.0\darabonba-array-0.1.0.jar;C:\Users\<USER>\.m2\repository\com\aliyun\tea\1.3.2\tea-1.3.2.jar;C:\Users\<USER>\.m2\repository\com\google\code\gson\gson\2.9.0\gson-2.9.0.jar;C:\Users\<USER>\.m2\repository\com\aliyun\tea-openapi\0.3.6\tea-openapi-0.3.6.jar;C:\Users\<USER>\.m2\repository\com\aliyun\credentials-java\0.3.10\credentials-java-0.3.10.jar;C:\Users\<USER>\.m2\repository\org\ini4j\ini4j\0.5.4\ini4j-0.5.4.jar;C:\Users\<USER>\.m2\repository\javax\xml\bind\jaxb-api\2.3.1\jaxb-api-2.3.1.jar;C:\Users\<USER>\.m2\repository\javax\activation\javax.activation-api\1.2.0\javax.activation-api-1.2.0.jar;C:\Users\<USER>\.m2\repository\com\sun\xml\bind\jaxb-core\2.3.0\jaxb-core-2.3.0.jar;C:\Users\<USER>\.m2\repository\com\sun\xml\bind\jaxb-impl\2.3.0\jaxb-impl-2.3.0.jar;C:\Users\<USER>\.m2\repository\com\aliyun\alibabacloud-gateway-spi\0.0.2\alibabacloud-gateway-spi-0.0.2.jar;C:\Users\<USER>\.m2\repository\com\aliyun\tea-xml\0.1.6\tea-xml-0.1.6.jar;C:\Users\<USER>\.m2\repository\org\dom4j\dom4j\2.0.3\dom4j-2.0.3.jar;C:\Users\<USER>\.m2\repository\org\jacoco\org.jacoco.agent\0.8.4\org.jacoco.agent-0.8.4-runtime.jar;C:\Users\<USER>\.m2\repository\io\jsonwebtoken\jjwt\0.9.1\jjwt-0.9.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-test\2.1.0.RELEASE\spring-boot-starter-test-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-test\2.1.0.RELEASE\spring-boot-test-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-test-autoconfigure\2.1.0.RELEASE\spring-boot-test-autoconfigure-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\com\jayway\jsonpath\json-path\2.4.0\json-path-2.4.0.jar;C:\Users\<USER>\.m2\repository\net\minidev\json-smart\2.3\json-smart-2.3.jar;C:\Users\<USER>\.m2\repository\net\minidev\accessors-smart\1.2\accessors-smart-1.2.jar;C:\Users\<USER>\.m2\repository\org\ow2\asm\asm\5.0.4\asm-5.0.4.jar;C:\Users\<USER>\.m2\repository\junit\junit\4.12\junit-4.12.jar;C:\Users\<USER>\.m2\repository\org\assertj\assertj-core\3.11.1\assertj-core-3.11.1.jar;C:\Users\<USER>\.m2\repository\org\mockito\mockito-core\2.23.0\mockito-core-2.23.0.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy\1.9.3\byte-buddy-1.9.3.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy-agent\1.9.3\byte-buddy-agent-1.9.3.jar;C:\Users\<USER>\.m2\repository\org\objenesis\objenesis\2.6\objenesis-2.6.jar;C:\Users\<USER>\.m2\repository\org\hamcrest\hamcrest-core\1.3\hamcrest-core-1.3.jar;C:\Users\<USER>\.m2\repository\org\hamcrest\hamcrest-library\1.3\hamcrest-library-1.3.jar;C:\Users\<USER>\.m2\repository\org\skyscreamer\jsonassert\1.5.0\jsonassert-1.5.0.jar;C:\Users\<USER>\.m2\repository\com\vaadin\external\google\android-json\0.0.20131108.vaadin1\android-json-0.0.20131108.vaadin1.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-test\5.1.2.RELEASE\spring-test-5.1.2.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\xmlunit\xmlunit-core\2.6.2\xmlunit-core-2.6.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-test\5.1.1.RELEASE\spring-security-test-5.1.1.RELEASE.jar;C:\Users\<USER>\.m2\repository\com\alibaba\cloud\spring-cloud-starter-alibaba-nacos-discovery\2.1.0.RELEASE\spring-cloud-starter-alibaba-nacos-discovery-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\com\alibaba\cloud\spring-cloud-alibaba-nacos-discovery\2.1.0.RELEASE\spring-cloud-alibaba-nacos-discovery-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-context\2.1.0.RELEASE\spring-cloud-context-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-starter-netflix-ribbon\2.1.0.RELEASE\spring-cloud-starter-netflix-ribbon-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-starter-netflix-archaius\2.1.0.RELEASE\spring-cloud-starter-netflix-archaius-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\com\netflix\ribbon\ribbon\2.3.0\ribbon-2.3.0.jar;C:\Users\<USER>\.m2\repository\com\netflix\ribbon\ribbon-transport\2.3.0\ribbon-transport-2.3.0.jar;C:\Users\<USER>\.m2\repository\io\reactivex\rxnetty-contexts\0.4.9\rxnetty-contexts-0.4.9.jar;C:\Users\<USER>\.m2\repository\io\reactivex\rxnetty-servo\0.4.9\rxnetty-servo-0.4.9.jar;C:\Users\<USER>\.m2\repository\javax\inject\javax.inject\1\javax.inject-1.jar;C:\Users\<USER>\.m2\repository\io\reactivex\rxnetty\0.4.9\rxnetty-0.4.9.jar;C:\Users\<USER>\.m2\repository\com\netflix\ribbon\ribbon-core\2.3.0\ribbon-core-2.3.0.jar;C:\Users\<USER>\.m2\repository\commons-lang\commons-lang\2.6\commons-lang-2.6.jar;C:\Users\<USER>\.m2\repository\com\netflix\ribbon\ribbon-httpclient\2.3.0\ribbon-httpclient-2.3.0.jar;C:\Users\<USER>\.m2\repository\com\sun\jersey\jersey-client\1.19.1\jersey-client-1.19.1.jar;C:\Users\<USER>\.m2\repository\com\sun\jersey\jersey-core\1.19.1\jersey-core-1.19.1.jar;C:\Users\<USER>\.m2\repository\javax\ws\rs\jsr311-api\1.1.1\jsr311-api-1.1.1.jar;C:\Users\<USER>\.m2\repository\com\sun\jersey\contribs\jersey-apache-client4\1.19.1\jersey-apache-client4-1.19.1.jar;C:\Users\<USER>\.m2\repository\com\netflix\servo\servo-core\0.12.21\servo-core-0.12.21.jar;C:\Users\<USER>\.m2\repository\com\netflix\netflix-commons\netflix-commons-util\0.3.0\netflix-commons-util-0.3.0.jar;C:\Users\<USER>\.m2\repository\com\netflix\ribbon\ribbon-loadbalancer\2.3.0\ribbon-loadbalancer-2.3.0.jar;C:\Users\<USER>\.m2\repository\com\netflix\netflix-commons\netflix-statistics\0.1.1\netflix-statistics-0.1.1.jar;C:\Users\<USER>\.m2\repository\io\reactivex\rxjava\1.3.8\rxjava-1.3.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-starter-openfeign\2.1.0.RELEASE\spring-cloud-starter-openfeign-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-starter\2.1.0.RELEASE\spring-cloud-starter-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-rsa\1.0.7.RELEASE\spring-security-rsa-1.0.7.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-openfeign-core\2.1.0.RELEASE\spring-cloud-openfeign-core-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-netflix-ribbon\2.1.0.RELEASE\spring-cloud-netflix-ribbon-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-netflix-archaius\2.1.0.RELEASE\spring-cloud-netflix-archaius-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-aop\2.1.0.RELEASE\spring-boot-starter-aop-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\aspectj\aspectjweaver\1.9.2\aspectjweaver-1.9.2.jar;C:\Users\<USER>\.m2\repository\io\github\openfeign\form\feign-form-spring\3.8.0\feign-form-spring-3.8.0.jar;C:\Users\<USER>\.m2\repository\io\github\openfeign\form\feign-form\3.8.0\feign-form-3.8.0.jar;C:\Users\<USER>\.m2\repository\commons-fileupload\commons-fileupload\1.4\commons-fileupload-1.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-commons\2.1.0.RELEASE\spring-cloud-commons-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-crypto\5.1.1.RELEASE\spring-security-crypto-5.1.1.RELEASE.jar;C:\Users\<USER>\.m2\repository\io\github\openfeign\feign-core\10.1.0\feign-core-10.1.0.jar;C:\Users\<USER>\.m2\repository\io\github\openfeign\feign-slf4j\10.1.0\feign-slf4j-10.1.0.jar;C:\Users\<USER>\.m2\repository\io\github\openfeign\feign-hystrix\10.1.0\feign-hystrix-10.1.0.jar;C:\Users\<USER>\.m2\repository\com\netflix\archaius\archaius-core\0.7.6\archaius-core-0.7.6.jar;C:\Users\<USER>\.m2\repository\commons-configuration\commons-configuration\1.8\commons-configuration-1.8.jar;C:\Users\<USER>\.m2\repository\com\netflix\hystrix\hystrix-core\1.5.18\hystrix-core-1.5.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-actuator\2.1.0.RELEASE\spring-boot-starter-actuator-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-actuator-autoconfigure\2.1.0.RELEASE\spring-boot-actuator-autoconfigure-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-actuator\2.1.0.RELEASE\spring-boot-actuator-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\io\micrometer\micrometer-core\1.1.0\micrometer-core-1.1.0.jar;C:\Users\<USER>\.m2\repository\org\hdrhistogram\HdrHistogram\2.1.9\HdrHistogram-2.1.9.jar;C:\Users\<USER>\.m2\repository\org\latencyutils\LatencyUtils\2.0.3\LatencyUtils-2.0.3.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-swagger2\2.7.0\springfox-swagger2-2.7.0.jar;C:\Users\<USER>\.m2\repository\io\swagger\swagger-annotations\1.5.14\swagger-annotations-1.5.14.jar;C:\Users\<USER>\.m2\repository\io\swagger\swagger-models\1.5.13\swagger-models-1.5.13.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-spi\2.7.0\springfox-spi-2.7.0.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-core\2.7.0\springfox-core-2.7.0.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-schema\2.7.0\springfox-schema-2.7.0.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-swagger-common\2.7.0\springfox-swagger-common-2.7.0.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-spring-web\2.7.0\springfox-spring-web-2.7.0.jar;C:\Users\<USER>\.m2\repository\org\reflections\reflections\0.9.11\reflections-0.9.11.jar;C:\Users\<USER>\.m2\repository\org\javassist\javassist\3.21.0-GA\javassist-3.21.0-GA.jar;C:\Users\<USER>\.m2\repository\com\google\guava\guava\31.1-jre\guava-31.1-jre.jar;C:\Users\<USER>\.m2\repository\com\google\guava\failureaccess\1.0.1\failureaccess-1.0.1.jar;C:\Users\<USER>\.m2\repository\com\google\guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar;C:\Users\<USER>\.m2\repository\com\google\code\findbugs\jsr305\3.0.2\jsr305-3.0.2.jar;C:\Users\<USER>\.m2\repository\org\checkerframework\checker-qual\3.12.0\checker-qual-3.12.0.jar;C:\Users\<USER>\.m2\repository\com\google\errorprone\error_prone_annotations\2.11.0\error_prone_annotations-2.11.0.jar;C:\Users\<USER>\.m2\repository\com\google\j2objc\j2objc-annotations\1.3\j2objc-annotations-1.3.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\classmate\1.4.0\classmate-1.4.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\plugin\spring-plugin-core\1.2.0.RELEASE\spring-plugin-core-1.2.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\plugin\spring-plugin-metadata\1.2.0.RELEASE\spring-plugin-metadata-1.2.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\mapstruct\mapstruct\1.1.0.Final\mapstruct-1.1.0.Final.jar;C:\Users\<USER>\.m2\repository\com\alibaba\boot\nacos-config-spring-boot-starter\0.2.10\nacos-config-spring-boot-starter-0.2.10.jar;C:\Users\<USER>\.m2\repository\com\alibaba\nacos\nacos-spring-context\1.1.1\nacos-spring-context-1.1.1.jar;C:\Users\<USER>\.m2\repository\com\alibaba\spring\spring-context-support\1.0.11\spring-context-support-1.0.11.jar;C:\Users\<USER>\.m2\repository\com\alibaba\nacos\nacos-client\1.4.3\nacos-client-1.4.3.jar;C:\Users\<USER>\.m2\repository\com\alibaba\nacos\nacos-common\1.4.3\nacos-common-1.4.3.jar;C:\Users\<USER>\.m2\repository\commons-io\commons-io\2.7\commons-io-2.7.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpasyncclient\4.1.4\httpasyncclient-4.1.4.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpcore-nio\4.4.10\httpcore-nio-4.4.10.jar;C:\Users\<USER>\.m2\repository\com\alibaba\nacos\nacos-api\1.4.3\nacos-api-1.4.3.jar;C:\Users\<USER>\.m2\repository\io\prometheus\simpleclient\0.5.0\simpleclient-0.5.0.jar;C:\Users\<USER>\.m2\repository\com\alibaba\boot\nacos-config-spring-boot-autoconfigure\0.2.10\nacos-config-spring-boot-autoconfigure-0.2.10.jar;C:\Users\<USER>\.m2\repository\com\alibaba\boot\nacos-spring-boot-base\0.2.10\nacos-spring-boot-base-0.2.10.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-databind\2.13.4.2\jackson-databind-2.13.4.2.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-annotations\2.13.3\jackson-annotations-2.13.3.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-core\2.13.3\jackson-core-2.13.3.jar;"/>
    <property name="java.vm.vendor" value="Temurin"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="user.variant" value=""/>
    <property name="java.vendor.url" value="https://adoptium.net/"/>
    <property name="user.timezone" value="Asia/Shanghai"/>
    <property name="java.vm.specification.version" value="1.8"/>
    <property name="os.name" value="Windows 10"/>
    <property name="user.country" value="CN"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="sun.boot.library.path" value="C:\Users\<USER>\.jdks\temurin-1.8.0_302\jre\bin"/>
    <property name="sun.java.command" value="C:\Users\<USER>\AppData\Local\Temp\surefire8315479486166934928\surefirebooter1636254333566548174.jar C:\Users\<USER>\AppData\Local\Temp\surefire8315479486166934928 2025-08-28T16-17-09_982-jvmRun1 surefire4770660101289872721tmp surefire_08537538664466262020tmp"/>
    <property name="test" value="JwtSignerServiceTest"/>
    <property name="surefire.test.class.path" value="C:\caidao\caidao_open_auth_service\target\test-classes;C:\caidao\caidao_open_auth_service\target\classes;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-web\2.1.0.RELEASE\spring-boot-starter-web-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter\2.1.0.RELEASE\spring-boot-starter-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot\2.1.0.RELEASE\spring-boot-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-logging\2.1.0.RELEASE\spring-boot-starter-logging-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-classic\1.2.11\logback-classic-1.2.11.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-core\1.2.11\logback-core-1.2.11.jar;C:\Users\<USER>\.m2\repository\org\slf4j\jul-to-slf4j\1.7.25\jul-to-slf4j-1.7.25.jar;C:\Users\<USER>\.m2\repository\javax\annotation\javax.annotation-api\1.3.2\javax.annotation-api-1.3.2.jar;C:\Users\<USER>\.m2\repository\org\yaml\snakeyaml\1.33\snakeyaml-1.33.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-json\2.1.0.RELEASE\spring-boot-starter-json-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.13.3\jackson-datatype-jdk8-2.13.3.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.13.3\jackson-datatype-jsr310-2.13.3.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.13.3\jackson-module-parameter-names-2.13.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-tomcat\2.1.0.RELEASE\spring-boot-starter-tomcat-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-el\9.0.12\tomcat-embed-el-9.0.12.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-websocket\9.0.12\tomcat-embed-websocket-9.0.12.jar;C:\Users\<USER>\.m2\repository\org\hibernate\validator\hibernate-validator\6.0.13.Final\hibernate-validator-6.0.13.Final.jar;C:\Users\<USER>\.m2\repository\javax\validation\validation-api\2.0.1.Final\validation-api-2.0.1.Final.jar;C:\Users\<USER>\.m2\repository\org\jboss\logging\jboss-logging\3.3.2.Final\jboss-logging-3.3.2.Final.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-web\5.1.2.RELEASE\spring-web-5.1.2.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-webmvc\5.1.2.RELEASE\spring-webmvc-5.1.2.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-expression\5.1.2.RELEASE\spring-expression-5.1.2.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-security\2.1.0.RELEASE\spring-boot-starter-security-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-aop\5.1.2.RELEASE\spring-aop-5.1.2.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-config\5.1.1.RELEASE\spring-security-config-5.1.1.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-web\5.1.1.RELEASE\spring-security-web-5.1.1.RELEASE.jar;C:\Users\<USER>\.m2\repository\com\caidaocloud\caidaocloud-commons\1.0.4-SNAPSHOT\caidaocloud-commons-1.0.4-SNAPSHOT.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-lang3\3.8.1\commons-lang3-3.8.1.jar;C:\Users\<USER>\.m2\repository\org\projectlombok\lombok\1.18.2\lombok-1.18.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context\5.1.2.RELEASE\spring-context-5.1.2.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\embed\tomcat-embed-core\9.0.12\tomcat-embed-core-9.0.12.jar;C:\Users\<USER>\.m2\repository\org\apache\tomcat\tomcat-annotations-api\9.0.12\tomcat-annotations-api-9.0.12.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-autoconfigure\2.1.0.RELEASE\spring-boot-autoconfigure-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\slf4j\slf4j-api\1.7.25\slf4j-api-1.7.25.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-swagger-ui\2.7.0\springfox-swagger-ui-2.7.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-configuration-processor\2.1.0.RELEASE\spring-boot-configuration-processor-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\com\squareup\okhttp3\okhttp\4.10.0\okhttp-4.10.0.jar;C:\Users\<USER>\.m2\repository\com\squareup\okio\okio-jvm\3.0.0\okio-jvm-3.0.0.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\kotlin\kotlin-stdlib\1.2.71\kotlin-stdlib-1.2.71.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\kotlin\kotlin-stdlib-common\1.2.71\kotlin-stdlib-common-1.2.71.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\annotations\13.0\annotations-13.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-mail\2.1.0.RELEASE\spring-boot-starter-mail-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context-support\5.1.2.RELEASE\spring-context-support-5.1.2.RELEASE.jar;C:\Users\<USER>\.m2\repository\com\sun\mail\javax.mail\1.6.2\javax.mail-1.6.2.jar;C:\Users\<USER>\.m2\repository\javax\activation\activation\1.1\activation-1.1.jar;C:\Users\<USER>\.m2\repository\commons-beanutils\commons-beanutils\1.9.4\commons-beanutils-1.9.4.jar;C:\Users\<USER>\.m2\repository\commons-logging\commons-logging\1.2\commons-logging-1.2.jar;C:\Users\<USER>\.m2\repository\commons-collections\commons-collections\3.2.2\commons-collections-3.2.2.jar;C:\Users\<USER>\.m2\repository\com\alibaba\fastjson\1.2.83\fastjson-1.2.83.jar;C:\Users\<USER>\.m2\repository\com\caidaocloud\metadata-sdk\1.1.5-SNAPSHOT\metadata-sdk-1.1.5-SNAPSHOT.jar;C:\Users\<USER>\.m2\repository\io\github\openfeign\feign-httpclient\10.1.0\feign-httpclient-10.1.0.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpclient\4.5.6\httpclient-4.5.6.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpcore\4.4.10\httpcore-4.4.10.jar;C:\Users\<USER>\.m2\repository\com\caidaocloud\paas-core\1.1.5-SNAPSHOT\paas-core-1.1.5-SNAPSHOT.jar;C:\Users\<USER>\.m2\repository\com\caidaocloud\galaxy-service-security\2.0.0-SNAPSHOT\galaxy-service-security-2.0.0-SNAPSHOT.jar;C:\Users\<USER>\.m2\repository\com\caidaocloud\galaxy-service-cache\2.0.0-SNAPSHOT\galaxy-service-cache-2.0.0-SNAPSHOT.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-data-redis\2.1.0.RELEASE\spring-boot-starter-data-redis-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-redis\2.1.2.RELEASE\spring-data-redis-2.1.2.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-keyvalue\2.1.2.RELEASE\spring-data-keyvalue-2.1.2.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\data\spring-data-commons\2.1.2.RELEASE\spring-data-commons-2.1.2.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-oxm\5.1.2.RELEASE\spring-oxm-5.1.2.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\kotlin\kotlin-stdlib-jdk8\1.2.71\kotlin-stdlib-jdk8-1.2.71.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\kotlin\kotlin-stdlib-jdk7\1.2.71\kotlin-stdlib-jdk7-1.2.71.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\kotlin\kotlin-reflect\1.2.71\kotlin-reflect-1.2.71.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\module\jackson-module-kotlin\2.13.3\jackson-module-kotlin-2.13.3.jar;C:\Users\<USER>\.m2\repository\com\github\qiujiayu\autoload-cache-spring-boot-starter\7.0.8\autoload-cache-spring-boot-starter-7.0.8.jar;C:\Users\<USER>\.m2\repository\com\github\qiujiayu\autoload-cache\7.0.8\autoload-cache-7.0.8.jar;C:\Users\<USER>\.m2\repository\io\lettuce\lettuce-core\5.1.2.RELEASE\lettuce-core-5.1.2.RELEASE.jar;C:\Users\<USER>\.m2\repository\io\projectreactor\reactor-core\3.2.2.RELEASE\reactor-core-3.2.2.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\reactivestreams\reactive-streams\1.0.2\reactive-streams-1.0.2.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-common\4.1.29.Final\netty-common-4.1.29.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-transport\4.1.29.Final\netty-transport-4.1.29.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-buffer\4.1.29.Final\netty-buffer-4.1.29.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-resolver\4.1.29.Final\netty-resolver-4.1.29.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-handler\4.1.29.Final\netty-handler-4.1.29.Final.jar;C:\Users\<USER>\.m2\repository\io\netty\netty-codec\4.1.29.Final\netty-codec-4.1.29.Final.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-pool2\2.6.0\commons-pool2-2.6.0.jar;C:\Users\<USER>\.m2\repository\com\googlecode\totallylazy\totallylazy\2.286\totallylazy-2.286.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\oauth\spring-security-oauth2\2.3.4.RELEASE\spring-security-oauth2-2.3.4.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-beans\5.1.2.RELEASE\spring-beans-5.1.2.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-core\5.1.2.RELEASE\spring-core-5.1.2.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jcl\5.1.2.RELEASE\spring-jcl-5.1.2.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-core\5.1.1.RELEASE\spring-security-core-5.1.1.RELEASE.jar;C:\Users\<USER>\.m2\repository\commons-codec\commons-codec\1.11\commons-codec-1.11.jar;C:\Users\<USER>\.m2\repository\org\codehaus\jackson\jackson-mapper-asl\1.9.13\jackson-mapper-asl-1.9.13.jar;C:\Users\<USER>\.m2\repository\org\codehaus\jackson\jackson-core-asl\1.9.13\jackson-core-asl-1.9.13.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-jwt\1.0.11.RELEASE\spring-security-jwt-1.0.11.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\bouncycastle\bcpkix-jdk15on\1.64\bcpkix-jdk15on-1.64.jar;C:\Users\<USER>\.m2\repository\org\bouncycastle\bcprov-jdk15on\1.64\bcprov-jdk15on-1.64.jar;C:\Users\<USER>\.m2\repository\mysql\mysql-connector-java\8.0.20\mysql-connector-java-8.0.20.jar;C:\Users\<USER>\.m2\repository\com\zaxxer\HikariCP\3.2.0\HikariCP-3.2.0.jar;C:\Users\<USER>\.m2\repository\com\baomidou\mybatis-plus-boot-starter\3.5.1\mybatis-plus-boot-starter-3.5.1.jar;C:\Users\<USER>\.m2\repository\com\baomidou\mybatis-plus\3.5.1\mybatis-plus-3.5.1.jar;C:\Users\<USER>\.m2\repository\com\baomidou\mybatis-plus-extension\3.5.1\mybatis-plus-extension-3.5.1.jar;C:\Users\<USER>\.m2\repository\com\baomidou\mybatis-plus-core\3.5.1\mybatis-plus-core-3.5.1.jar;C:\Users\<USER>\.m2\repository\com\baomidou\mybatis-plus-annotation\3.5.1\mybatis-plus-annotation-3.5.1.jar;C:\Users\<USER>\.m2\repository\com\github\jsqlparser\jsqlparser\4.3\jsqlparser-4.3.jar;C:\Users\<USER>\.m2\repository\org\mybatis\mybatis\3.5.9\mybatis-3.5.9.jar;C:\Users\<USER>\.m2\repository\org\mybatis\mybatis-spring\2.0.6\mybatis-spring-2.0.6.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-jdbc\2.1.0.RELEASE\spring-boot-starter-jdbc-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jdbc\5.1.2.RELEASE\spring-jdbc-5.1.2.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-tx\5.1.2.RELEASE\spring-tx-5.1.2.RELEASE.jar;C:\Users\<USER>\.m2\repository\com\aliyun\kms20160120\1.2.3\kms20160120-1.2.3.jar;C:\Users\<USER>\.m2\repository\com\aliyun\tea-util\0.2.23\tea-util-0.2.23.jar;C:\Users\<USER>\.m2\repository\com\aliyun\endpoint-util\0.0.7\endpoint-util-0.0.7.jar;C:\Users\<USER>\.m2\repository\com\aliyun\openapiutil\0.2.1\openapiutil-0.2.1.jar;C:\Users\<USER>\.m2\repository\com\aliyun\alibabacloud-gateway-pop\0.0.6\alibabacloud-gateway-pop-0.0.6.jar;C:\Users\<USER>\.m2\repository\com\aliyun\darabonba-encode-util\0.0.2\darabonba-encode-util-0.0.2.jar;C:\Users\<USER>\.m2\repository\com\aliyun\darabonba-signature-util\0.0.4\darabonba-signature-util-0.0.4.jar;C:\Users\<USER>\.m2\repository\com\aliyun\darabonba-string\0.0.5\darabonba-string-0.0.5.jar;C:\Users\<USER>\.m2\repository\com\aliyun\darabonba-map\0.0.1\darabonba-map-0.0.1.jar;C:\Users\<USER>\.m2\repository\com\aliyun\darabonba-array\0.1.0\darabonba-array-0.1.0.jar;C:\Users\<USER>\.m2\repository\com\aliyun\tea\1.3.2\tea-1.3.2.jar;C:\Users\<USER>\.m2\repository\com\google\code\gson\gson\2.9.0\gson-2.9.0.jar;C:\Users\<USER>\.m2\repository\com\aliyun\tea-openapi\0.3.6\tea-openapi-0.3.6.jar;C:\Users\<USER>\.m2\repository\com\aliyun\credentials-java\0.3.10\credentials-java-0.3.10.jar;C:\Users\<USER>\.m2\repository\org\ini4j\ini4j\0.5.4\ini4j-0.5.4.jar;C:\Users\<USER>\.m2\repository\javax\xml\bind\jaxb-api\2.3.1\jaxb-api-2.3.1.jar;C:\Users\<USER>\.m2\repository\javax\activation\javax.activation-api\1.2.0\javax.activation-api-1.2.0.jar;C:\Users\<USER>\.m2\repository\com\sun\xml\bind\jaxb-core\2.3.0\jaxb-core-2.3.0.jar;C:\Users\<USER>\.m2\repository\com\sun\xml\bind\jaxb-impl\2.3.0\jaxb-impl-2.3.0.jar;C:\Users\<USER>\.m2\repository\com\aliyun\alibabacloud-gateway-spi\0.0.2\alibabacloud-gateway-spi-0.0.2.jar;C:\Users\<USER>\.m2\repository\com\aliyun\tea-xml\0.1.6\tea-xml-0.1.6.jar;C:\Users\<USER>\.m2\repository\org\dom4j\dom4j\2.0.3\dom4j-2.0.3.jar;C:\Users\<USER>\.m2\repository\org\jacoco\org.jacoco.agent\0.8.4\org.jacoco.agent-0.8.4-runtime.jar;C:\Users\<USER>\.m2\repository\io\jsonwebtoken\jjwt\0.9.1\jjwt-0.9.1.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-test\2.1.0.RELEASE\spring-boot-starter-test-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-test\2.1.0.RELEASE\spring-boot-test-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-test-autoconfigure\2.1.0.RELEASE\spring-boot-test-autoconfigure-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\com\jayway\jsonpath\json-path\2.4.0\json-path-2.4.0.jar;C:\Users\<USER>\.m2\repository\net\minidev\json-smart\2.3\json-smart-2.3.jar;C:\Users\<USER>\.m2\repository\net\minidev\accessors-smart\1.2\accessors-smart-1.2.jar;C:\Users\<USER>\.m2\repository\org\ow2\asm\asm\5.0.4\asm-5.0.4.jar;C:\Users\<USER>\.m2\repository\junit\junit\4.12\junit-4.12.jar;C:\Users\<USER>\.m2\repository\org\assertj\assertj-core\3.11.1\assertj-core-3.11.1.jar;C:\Users\<USER>\.m2\repository\org\mockito\mockito-core\2.23.0\mockito-core-2.23.0.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy\1.9.3\byte-buddy-1.9.3.jar;C:\Users\<USER>\.m2\repository\net\bytebuddy\byte-buddy-agent\1.9.3\byte-buddy-agent-1.9.3.jar;C:\Users\<USER>\.m2\repository\org\objenesis\objenesis\2.6\objenesis-2.6.jar;C:\Users\<USER>\.m2\repository\org\hamcrest\hamcrest-core\1.3\hamcrest-core-1.3.jar;C:\Users\<USER>\.m2\repository\org\hamcrest\hamcrest-library\1.3\hamcrest-library-1.3.jar;C:\Users\<USER>\.m2\repository\org\skyscreamer\jsonassert\1.5.0\jsonassert-1.5.0.jar;C:\Users\<USER>\.m2\repository\com\vaadin\external\google\android-json\0.0.20131108.vaadin1\android-json-0.0.20131108.vaadin1.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-test\5.1.2.RELEASE\spring-test-5.1.2.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\xmlunit\xmlunit-core\2.6.2\xmlunit-core-2.6.2.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-test\5.1.1.RELEASE\spring-security-test-5.1.1.RELEASE.jar;C:\Users\<USER>\.m2\repository\com\alibaba\cloud\spring-cloud-starter-alibaba-nacos-discovery\2.1.0.RELEASE\spring-cloud-starter-alibaba-nacos-discovery-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\com\alibaba\cloud\spring-cloud-alibaba-nacos-discovery\2.1.0.RELEASE\spring-cloud-alibaba-nacos-discovery-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-context\2.1.0.RELEASE\spring-cloud-context-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-starter-netflix-ribbon\2.1.0.RELEASE\spring-cloud-starter-netflix-ribbon-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-starter-netflix-archaius\2.1.0.RELEASE\spring-cloud-starter-netflix-archaius-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\com\netflix\ribbon\ribbon\2.3.0\ribbon-2.3.0.jar;C:\Users\<USER>\.m2\repository\com\netflix\ribbon\ribbon-transport\2.3.0\ribbon-transport-2.3.0.jar;C:\Users\<USER>\.m2\repository\io\reactivex\rxnetty-contexts\0.4.9\rxnetty-contexts-0.4.9.jar;C:\Users\<USER>\.m2\repository\io\reactivex\rxnetty-servo\0.4.9\rxnetty-servo-0.4.9.jar;C:\Users\<USER>\.m2\repository\javax\inject\javax.inject\1\javax.inject-1.jar;C:\Users\<USER>\.m2\repository\io\reactivex\rxnetty\0.4.9\rxnetty-0.4.9.jar;C:\Users\<USER>\.m2\repository\com\netflix\ribbon\ribbon-core\2.3.0\ribbon-core-2.3.0.jar;C:\Users\<USER>\.m2\repository\commons-lang\commons-lang\2.6\commons-lang-2.6.jar;C:\Users\<USER>\.m2\repository\com\netflix\ribbon\ribbon-httpclient\2.3.0\ribbon-httpclient-2.3.0.jar;C:\Users\<USER>\.m2\repository\com\sun\jersey\jersey-client\1.19.1\jersey-client-1.19.1.jar;C:\Users\<USER>\.m2\repository\com\sun\jersey\jersey-core\1.19.1\jersey-core-1.19.1.jar;C:\Users\<USER>\.m2\repository\javax\ws\rs\jsr311-api\1.1.1\jsr311-api-1.1.1.jar;C:\Users\<USER>\.m2\repository\com\sun\jersey\contribs\jersey-apache-client4\1.19.1\jersey-apache-client4-1.19.1.jar;C:\Users\<USER>\.m2\repository\com\netflix\servo\servo-core\0.12.21\servo-core-0.12.21.jar;C:\Users\<USER>\.m2\repository\com\netflix\netflix-commons\netflix-commons-util\0.3.0\netflix-commons-util-0.3.0.jar;C:\Users\<USER>\.m2\repository\com\netflix\ribbon\ribbon-loadbalancer\2.3.0\ribbon-loadbalancer-2.3.0.jar;C:\Users\<USER>\.m2\repository\com\netflix\netflix-commons\netflix-statistics\0.1.1\netflix-statistics-0.1.1.jar;C:\Users\<USER>\.m2\repository\io\reactivex\rxjava\1.3.8\rxjava-1.3.8.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-starter-openfeign\2.1.0.RELEASE\spring-cloud-starter-openfeign-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-starter\2.1.0.RELEASE\spring-cloud-starter-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-rsa\1.0.7.RELEASE\spring-security-rsa-1.0.7.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-openfeign-core\2.1.0.RELEASE\spring-cloud-openfeign-core-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-netflix-ribbon\2.1.0.RELEASE\spring-cloud-netflix-ribbon-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-netflix-archaius\2.1.0.RELEASE\spring-cloud-netflix-archaius-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-aop\2.1.0.RELEASE\spring-boot-starter-aop-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\aspectj\aspectjweaver\1.9.2\aspectjweaver-1.9.2.jar;C:\Users\<USER>\.m2\repository\io\github\openfeign\form\feign-form-spring\3.8.0\feign-form-spring-3.8.0.jar;C:\Users\<USER>\.m2\repository\io\github\openfeign\form\feign-form\3.8.0\feign-form-3.8.0.jar;C:\Users\<USER>\.m2\repository\commons-fileupload\commons-fileupload\1.4\commons-fileupload-1.4.jar;C:\Users\<USER>\.m2\repository\org\springframework\cloud\spring-cloud-commons\2.1.0.RELEASE\spring-cloud-commons-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\security\spring-security-crypto\5.1.1.RELEASE\spring-security-crypto-5.1.1.RELEASE.jar;C:\Users\<USER>\.m2\repository\io\github\openfeign\feign-core\10.1.0\feign-core-10.1.0.jar;C:\Users\<USER>\.m2\repository\io\github\openfeign\feign-slf4j\10.1.0\feign-slf4j-10.1.0.jar;C:\Users\<USER>\.m2\repository\io\github\openfeign\feign-hystrix\10.1.0\feign-hystrix-10.1.0.jar;C:\Users\<USER>\.m2\repository\com\netflix\archaius\archaius-core\0.7.6\archaius-core-0.7.6.jar;C:\Users\<USER>\.m2\repository\commons-configuration\commons-configuration\1.8\commons-configuration-1.8.jar;C:\Users\<USER>\.m2\repository\com\netflix\hystrix\hystrix-core\1.5.18\hystrix-core-1.5.18.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-starter-actuator\2.1.0.RELEASE\spring-boot-starter-actuator-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-actuator-autoconfigure\2.1.0.RELEASE\spring-boot-actuator-autoconfigure-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\boot\spring-boot-actuator\2.1.0.RELEASE\spring-boot-actuator-2.1.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\io\micrometer\micrometer-core\1.1.0\micrometer-core-1.1.0.jar;C:\Users\<USER>\.m2\repository\org\hdrhistogram\HdrHistogram\2.1.9\HdrHistogram-2.1.9.jar;C:\Users\<USER>\.m2\repository\org\latencyutils\LatencyUtils\2.0.3\LatencyUtils-2.0.3.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-swagger2\2.7.0\springfox-swagger2-2.7.0.jar;C:\Users\<USER>\.m2\repository\io\swagger\swagger-annotations\1.5.14\swagger-annotations-1.5.14.jar;C:\Users\<USER>\.m2\repository\io\swagger\swagger-models\1.5.13\swagger-models-1.5.13.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-spi\2.7.0\springfox-spi-2.7.0.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-core\2.7.0\springfox-core-2.7.0.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-schema\2.7.0\springfox-schema-2.7.0.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-swagger-common\2.7.0\springfox-swagger-common-2.7.0.jar;C:\Users\<USER>\.m2\repository\io\springfox\springfox-spring-web\2.7.0\springfox-spring-web-2.7.0.jar;C:\Users\<USER>\.m2\repository\org\reflections\reflections\0.9.11\reflections-0.9.11.jar;C:\Users\<USER>\.m2\repository\org\javassist\javassist\3.21.0-GA\javassist-3.21.0-GA.jar;C:\Users\<USER>\.m2\repository\com\google\guava\guava\31.1-jre\guava-31.1-jre.jar;C:\Users\<USER>\.m2\repository\com\google\guava\failureaccess\1.0.1\failureaccess-1.0.1.jar;C:\Users\<USER>\.m2\repository\com\google\guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar;C:\Users\<USER>\.m2\repository\com\google\code\findbugs\jsr305\3.0.2\jsr305-3.0.2.jar;C:\Users\<USER>\.m2\repository\org\checkerframework\checker-qual\3.12.0\checker-qual-3.12.0.jar;C:\Users\<USER>\.m2\repository\com\google\errorprone\error_prone_annotations\2.11.0\error_prone_annotations-2.11.0.jar;C:\Users\<USER>\.m2\repository\com\google\j2objc\j2objc-annotations\1.3\j2objc-annotations-1.3.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\classmate\1.4.0\classmate-1.4.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\plugin\spring-plugin-core\1.2.0.RELEASE\spring-plugin-core-1.2.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\plugin\spring-plugin-metadata\1.2.0.RELEASE\spring-plugin-metadata-1.2.0.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\mapstruct\mapstruct\1.1.0.Final\mapstruct-1.1.0.Final.jar;C:\Users\<USER>\.m2\repository\com\alibaba\boot\nacos-config-spring-boot-starter\0.2.10\nacos-config-spring-boot-starter-0.2.10.jar;C:\Users\<USER>\.m2\repository\com\alibaba\nacos\nacos-spring-context\1.1.1\nacos-spring-context-1.1.1.jar;C:\Users\<USER>\.m2\repository\com\alibaba\spring\spring-context-support\1.0.11\spring-context-support-1.0.11.jar;C:\Users\<USER>\.m2\repository\com\alibaba\nacos\nacos-client\1.4.3\nacos-client-1.4.3.jar;C:\Users\<USER>\.m2\repository\com\alibaba\nacos\nacos-common\1.4.3\nacos-common-1.4.3.jar;C:\Users\<USER>\.m2\repository\commons-io\commons-io\2.7\commons-io-2.7.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpasyncclient\4.1.4\httpasyncclient-4.1.4.jar;C:\Users\<USER>\.m2\repository\org\apache\httpcomponents\httpcore-nio\4.4.10\httpcore-nio-4.4.10.jar;C:\Users\<USER>\.m2\repository\com\alibaba\nacos\nacos-api\1.4.3\nacos-api-1.4.3.jar;C:\Users\<USER>\.m2\repository\io\prometheus\simpleclient\0.5.0\simpleclient-0.5.0.jar;C:\Users\<USER>\.m2\repository\com\alibaba\boot\nacos-config-spring-boot-autoconfigure\0.2.10\nacos-config-spring-boot-autoconfigure-0.2.10.jar;C:\Users\<USER>\.m2\repository\com\alibaba\boot\nacos-spring-boot-base\0.2.10\nacos-spring-boot-base-0.2.10.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-databind\2.13.4.2\jackson-databind-2.13.4.2.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-annotations\2.13.3\jackson-annotations-2.13.3.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-core\2.13.3\jackson-core-2.13.3.jar;"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="C:\Users\<USER>\Users\admin\.jdks\temurin-1.8.0_302\jre"/>
    <property name="basedir" value="C:\caidao\caidao_open_auth_service"/>
    <property name="file.separator" value="\"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="java.awt.graphicsenv" value="sun.awt.Win32GraphicsEnvironment"/>
    <property name="surefire.real.class.path" value="C:\Users\<USER>\AppData\Local\Temp\surefire8315479486166934928\surefirebooter1636254333566548174.jar"/>
    <property name="sun.boot.class.path" value="C:\Users\<USER>\.jdks\temurin-1.8.0_302\jre\lib\resources.jar;C:\Users\<USER>\.jdks\temurin-1.8.0_302\jre\lib\rt.jar;C:\Users\<USER>\.jdks\temurin-1.8.0_302\jre\lib\sunrsasign.jar;C:\Users\<USER>\.jdks\temurin-1.8.0_302\jre\lib\jsse.jar;C:\Users\<USER>\.jdks\temurin-1.8.0_302\jre\lib\jce.jar;C:\Users\<USER>\.jdks\temurin-1.8.0_302\jre\lib\charsets.jar;C:\Users\<USER>\.jdks\temurin-1.8.0_302\jre\lib\jfr.jar;C:\Users\<USER>\.jdks\temurin-1.8.0_302\jre\classes"/>
    <property name="user.script" value=""/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="java.runtime.version" value="1.8.0_302-b08"/>
    <property name="user.name" value="admin"/>
    <property name="path.separator" value=";"/>
    <property name="os.version" value="10.0"/>
    <property name="java.endorsed.dirs" value="C:\Users\<USER>\.jdks\temurin-1.8.0_302\jre\lib\endorsed"/>
    <property name="java.runtime.name" value="OpenJDK Runtime Environment"/>
    <property name="file.encoding" value="GBK"/>
    <property name="java.vm.name" value="OpenJDK 64-Bit Server VM"/>
    <property name="localRepository" value="C:\Users\<USER>\.m2\repository"/>
    <property name="java.vendor.url.bug" value="https://github.com/adoptium/adoptium-support/issues"/>
    <property name="java.io.tmpdir" value="C:\Users\<USER>\AppData\Local\Temp\"/>
    <property name="java.version" value="1.8.0_302"/>
    <property name="user.dir" value="C:\caidao\caidao_open_auth_service"/>
    <property name="os.arch" value="amd64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="java.awt.printerjob" value="sun.awt.windows.WPrinterJob"/>
    <property name="sun.os.patch.level" value=""/>
    <property name="java.library.path" value="C:\Users\<USER>\.jdks\temurin-1.8.0_302\jre\bin;C:\windows\Sun\Java\bin;C:\windows\system32;C:\windows;C:\windows\system32;C:\windows;C:\windows\System32\Wbem;C:\windows\System32\WindowsPowerShell\v1.0\;C:\windows\System32\OpenSSH\;C:\Program Files\Git\cmd;C:\Users\<USER>\.jdks\temurin-1.8.0_302\bin;C:\Users\<USER>\.jdks\temurin-1.8.0_302\jre\bin;C:\Program Files\JetBrains\IntelliJ IDEA 2021.2.2\plugins\maven\lib\maven3\bin;C:\Program Files\Kubernetes\Minikube;;C:\minikube;C:\Program Files\Docker\Docker\resources\bin;C:\ProgramData\DockerDesktop\version-bin;C:\Program Files (x86)\Tencent\微信web开发者工具\dll;C:\Program Files\nodejs\;C:\Program Files\cursor\resources\app\bin;C:\Users\<USER>\.cargo\bin;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python310\;C:\Users\<USER>\scoop\shims;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\JetBrains\IntelliJ IDEA 2021.2.2\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\Programs\Windsurf\bin;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand;."/>
    <property name="java.vm.info" value="mixed mode"/>
    <property name="java.vendor" value="Temurin"/>
    <property name="java.vm.version" value="25.302-b08"/>
    <property name="java.ext.dirs" value="C:\Users\<USER>\.jdks\temurin-1.8.0_302\jre\lib\ext;C:\windows\Sun\Java\lib\ext"/>
    <property name="sun.io.unicode.encoding" value="UnicodeLittle"/>
    <property name="java.class.version" value="52.0"/>
  </properties>
  <testcase name="testSimpleJwtSigner" classname="com.caidaocloud.open.auth.service.infrastructure.config.jwt.JwtSignerServiceTest" time="1.966"/>
</testsuite>